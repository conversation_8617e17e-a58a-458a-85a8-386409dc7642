{"name": "@supabase/postgrest-js", "version": "0.37.4", "description": "Isomorphic PostgREST client", "keywords": ["postgrest", "supabase"], "homepage": "https://github.com/supabase/postgrest-js", "bugs": "https://github.com/supabase/postgrest-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "repository": "supabase/postgrest-js", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist docs", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "genversion src/lib/version.ts --es6 && run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "test": "run-s test:db && jest --runInBand", "test:clean": "cd test/db && docker-compose down", "test:db": "cd test/db && docker-compose down && docker-compose up -d && wait-for-localhost 3000", "docs": "typedoc --mode file --target ES6 --theme minimal", "docs:json": "typedoc --json docs/spec.json --mode modules --includeDeclarations --excludeExternals"}, "dependencies": {"cross-fetch": "^3.1.5"}, "devDependencies": {"@types/jest": "^26.0.13", "@types/node-fetch": "^2.5.7", "genversion": "^3.0.1", "jest": "^26.4.1", "node-abort-controller": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "ts-jest": "^26.3.0", "typedoc": "^0.22.15", "typescript": "^4.0.2", "wait-for-localhost-cli": "^2.0.0"}}