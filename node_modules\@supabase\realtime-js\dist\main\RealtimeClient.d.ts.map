{"version": 3, "file": "RealtimeClient.d.ts", "sourceRoot": "", "sources": ["../../src/RealtimeClient.ts"], "names": [], "mappings": "AACA,OAAO,EAQL,gBAAgB,EACjB,MAAM,iBAAiB,CAAA;AACxB,OAAO,KAAK,MAAM,aAAa,CAAA;AAC/B,OAAO,UAAU,MAAM,kBAAkB,CAAA;AACzC,OAAO,oBAAoB,MAAM,wBAAwB,CAAA;AAEzD,oBAAY,OAAO,GAAG;IACpB,SAAS,CAAC,EAAE,SAAS,CAAA;IACrB,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,mBAAmB,CAAC,EAAE,MAAM,CAAA;IAC5B,iBAAiB,CAAC,EAAE,MAAM,CAAA;IAC1B,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,gBAAgB,CAAC,EAAE,QAAQ,CAAA;IAC3B,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;IACnC,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;CACnC,CAAA;AACD,aAAK,OAAO,GAAG;IACb,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,GAAG,CAAA;IACZ,GAAG,EAAE,MAAM,CAAA;IACX,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,aAAK,aAAa,GAAG;IACnB,aAAa,CAAC,EAAE,OAAO,CAAA;IACvB,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CACnB,CAAA;AAID,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,WAAW,EAAE,MAAM,GAAG,IAAI,CAAO;IACjC,QAAQ,EAAE,oBAAoB,EAAE,CAAK;IACrC,QAAQ,EAAE,MAAM,CAAK;IACrB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAkB;IACrD,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAK;IACvC,OAAO,EAAE,MAAM,CAAkB;IACjC,SAAS,EAAE,GAAG,CAAe;IAC7B,mBAAmB,EAAE,MAAM,CAAQ;IACnC,iBAAiB,EAAE,MAAM,CAAQ;IACjC,cAAc,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,GAAG,SAAS,CAAY;IACtE,mBAAmB,EAAE,MAAM,GAAG,IAAI,CAAO;IACzC,GAAG,EAAE,MAAM,CAAI;IACf,cAAc,EAAE,KAAK,CAAA;IACrB,MAAM,EAAE,QAAQ,CAAO;IACvB,MAAM,EAAE,QAAQ,CAAA;IAChB,MAAM,EAAE,QAAQ,CAAA;IAChB,gBAAgB,EAAE,QAAQ,CAAA;IAC1B,IAAI,EAAE,SAAS,GAAG,IAAI,CAAO;IAC7B,UAAU,EAAE,QAAQ,EAAE,CAAK;IAC3B,UAAU,EAAE,UAAU,CAAmB;IACzC,oBAAoB,EAAE;QACpB,IAAI,EAAE,QAAQ,EAAE,CAAA;QAChB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,OAAO,EAAE,QAAQ,EAAE,CAAA;KACpB,CAKA;IAED;;;;;;;;;;;;;;OAcG;gBACS,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO;IAgC/C;;OAEG;IACH,OAAO,IAAI,IAAI;IAiBf;;;;;OAKG;IACH,UAAU,CACR,IAAI,CAAC,EAAE,MAAM,EACb,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC;QAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,OAAO,CAAA;KAAE,CAAC;IAsBlD;;;;OAIG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG;IAIzC;;;;;;;OAOG;IACH,MAAM,CAAC,QAAQ,EAAE,QAAQ;IAIzB;;;;;;;OAOG;IACH,OAAO,CAAC,QAAQ,EAAE,QAAQ;IAI1B;;;;;;;OAOG;IACH,OAAO,CAAC,QAAQ,EAAE,QAAQ;IAI1B;;;;;;;OAOG;IACH,SAAS,CAAC,QAAQ,EAAE,QAAQ;IAI5B;;OAEG;IACH,eAAe,IAAI,gBAAgB;IAanC;;OAEG;IACH,WAAW,IAAI,OAAO;IAItB;;;;OAIG;IACH,MAAM,CAAC,OAAO,EAAE,oBAAoB;IAMpC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,GAAE,aAAkB,GAAG,oBAAoB;IAM5E;;;;OAIG;IACH,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAezB,aAAa,CAAC,UAAU,EAAE;QAAE,IAAI,EAAE,GAAG,CAAA;KAAE;IA2BvC;;OAEG;IACH,WAAW,IAAI,MAAM;IAOrB;;OAEG;IACH,OAAO,IAAI,MAAM;IAWjB;;;;OAIG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAY5B;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAUnC,OAAO,CAAC,WAAW;IAYnB,OAAO,CAAC,YAAY;IAQpB,OAAO,CAAC,YAAY;IAMpB,OAAO,CAAC,iBAAiB;IAMzB,OAAO,CAAC,aAAa;IAarB,OAAO,CAAC,gBAAgB;IAOxB,OAAO,CAAC,cAAc;CAsBvB"}