{"version": 3, "file": "SupabaseClient.js", "sourceRoot": "", "sources": ["../../src/SupabaseClient.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAA;AAC9D,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,MAAM,eAAe,CAAA;AAE7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;AAC7D,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAA;AACjE,OAAO,EAAE,qBAAqB,EAAE,MAAM,sBAAsB,CAAA;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AACxD,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,OAAO,EAAE,cAAc,EAA+C,MAAM,uBAAuB,CAAA;AAEnG,MAAM,eAAe,GAAG;IACtB,MAAM,EAAE,QAAQ;IAChB,gBAAgB,EAAE,IAAI;IACtB,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;IACxB,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,eAAe;CACzB,CAAA;AAED;;;;GAIG;AACH,MAAM,CAAC,OAAO,OAAO,cAAc;IAsBjC;;;;;;;;;;;;OAYG;IACH,YACY,WAAmB,EACnB,WAAmB,EAC7B,OAA+B;QAFrB,gBAAW,GAAX,WAAW,CAAQ;QACnB,gBAAW,GAAX,WAAW,CAAQ;QAG7B,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7D,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAE7D,MAAM,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAA;QACpD,MAAM,QAAQ,mCAAQ,eAAe,GAAK,OAAO,CAAE,CAAA;QAEnD,IAAI,CAAC,OAAO,GAAG,GAAG,YAAY,UAAU,CAAA;QACxC,IAAI,CAAC,WAAW,GAAG,GAAG,YAAY,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACtE,IAAI,CAAC,OAAO,GAAG,GAAG,YAAY,UAAU,CAAA;QACxC,IAAI,CAAC,UAAU,GAAG,GAAG,YAAY,aAAa,CAAA;QAE9C,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;QACtE,IAAI,UAAU,EAAE;YACd,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,CAAC,YAAY,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,cAAc,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAA;SAC7E;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,GAAG,YAAY,eAAe,CAAA;SACnD;QAED,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;QACjC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAA;QAC3B,IAAI,CAAC,OAAO,mCAAQ,eAAe,GAAK,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAE,CAAA;QAC1D,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,IAAI,KAAK,CAAA;QAE9D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;QAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,iBAAG,OAAO,EAAE,IAAI,CAAC,OAAO,IAAK,QAAQ,CAAC,QAAQ,EAAG,CAAA;QAEzF,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAE/B,qFAAqF;QACrF,kDAAkD;QAClD,qDAAqD;QACrD,sEAAsE;IACxE,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE;YAC5C,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,WAAW,EAAE,IAAI,CAAC,KAAK;SACxB,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IACvF,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAU,KAAa;QACzB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE,CAAA;QACtC,OAAO,IAAI,oBAAoB,CAAI,GAAG,EAAE;YACtC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;SAC5C,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,GAAG,CACD,EAAU,EACV,MAAe,EACf,EACE,IAAI,GAAG,KAAK,EACZ,KAAK,GAAG,IAAI,MAC4D,EAAE;QAE5E,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;QACxC,OAAO,IAAI,CAAC,GAAG,CAAI,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;IACjD,CAAC;IAED;;;OAGG;IACG,sBAAsB;;YAG1B,MAAM,OAAO,GAA2B,IAAI,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,CAAA;YACvE,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAA;YACzE,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAExD,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE;gBACzC,OAAO;oBACL,IAAI,EAAE,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;oBAClC,KAAK;iBACN,CAAA;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;KAAA;IAED;;;;OAIG;IACG,kBAAkB,CACtB,YAAkC;;YAElC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAA;YAC7D,MAAM,OAAO,GAA2B,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAA;YAErE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;YAE1D,OAAO,EAAE,IAAI,EAAE,EAAE,iBAAiB,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,CAAA;QAC7D,CAAC;KAAA;IAEa,kBAAkB,CAC9B,YAAkC;;YAElC,IAAI,KAAK,GAAG,IAAI,CAAA;YAEhB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE;gBAC5B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAA;gBAC/E,KAAK,GAAG,UAAU,CAAA;aACnB;YAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAElC,OAAO,EAAE,KAAK,EAAE,CAAA;QAClB,CAAC;KAAA;IAEO,wBAAwB,CAC9B,YAAkC;QAElC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,YAAY;iBACT,WAAW,EAAE;iBACb,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC7C,OAAO,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBACtD,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAA;QACzE,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAkC,CAAA;IACzD,CAAC;IAEO,uBAAuB,CAAC,EAC9B,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,OAAO,EACP,KAAK,EACL,aAAa,EACb,QAAQ,GACc;QACtB,MAAM,WAAW,GAAG;YAClB,aAAa,EAAE,UAAU,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;SAC9B,CAAA;QACD,OAAO,IAAI,kBAAkB,CAAC;YAC5B,GAAG,EAAE,IAAI,CAAC,OAAO;YACjB,OAAO,kCAAO,OAAO,GAAK,WAAW,CAAE;YACvC,gBAAgB;YAChB,cAAc;YACd,kBAAkB;YAClB,YAAY;YACZ,KAAK;YACL,aAAa;YACb,QAAQ;SACT,CAAC,CAAA;IACJ,CAAC;IAEO,mBAAmB,CAAC,OAA+B;QACzD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,kCACrC,OAAO,KACV,MAAM,kCAAO,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAE,MAAM,EAAE,IAAI,CAAC,WAAW,OACtD,CAAA;IACJ,CAAC;IAEO,oBAAoB;QAC1B,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE;YACvC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,kBAAkB;SACtC,CAAC,CAAA;IACJ,CAAC;IAEO,eAAe;;QACrB,MAAM,OAAO,qBAAuB,IAAI,CAAC,OAAO,CAAE,CAAA;QAClD,MAAM,UAAU,GAAG,MAAA,MAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,0CAAE,YAAY,mCAAI,IAAI,CAAC,WAAW,CAAA;QACxE,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAA;QACpC,OAAO,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,UAAU,UAAU,EAAE,CAAA;QAC7E,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,wBAAwB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAA,EAAE;YAC/D,OAAO,IAAI,CAAA;SACZ;QAED,IAAI;YACF,OAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAe,EAAE,EAAE;;gBAC7D,IAAI,CAAC,CAAC,GAAG,KAAK,WAAW,EAAE;oBACzB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;oBACjD,MAAM,WAAW,GACf,MAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,cAAc,0CAAE,YAAY,mCAAI,SAAS,CAAA;oBACvD,MAAM,mBAAmB,GAAG,MAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,0CAAE,YAAY,CAAA;oBAC7D,IAAI,CAAC,WAAW,EAAE;wBAChB,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;qBAC/D;yBAAM,IAAI,CAAC,mBAAmB,IAAI,WAAW,EAAE;wBAC9C,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;qBAC9D;yBAAM,IAAI,mBAAmB,KAAK,WAAW,EAAE;wBAC9C,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;qBACpE;iBACF;YACH,CAAC,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,IAAI,CAAA;SACZ;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC5D,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,EAAE,QAAQ,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,mBAAmB,CACzB,KAAsB,EACtB,KAAyB,EACzB,MAA4B;QAE5B,IACE,CAAC,KAAK,KAAK,iBAAiB,IAAI,KAAK,KAAK,WAAW,CAAC;YACtD,IAAI,CAAC,kBAAkB,KAAK,KAAK,EACjC;YACA,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAM,CAAC,CAAA;YAC7B,0EAA0E;YAC1E,iDAAiD;YACjD,IAAI,MAAM,IAAI,SAAS;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAM,CAAC,CAAA;YAElD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;SAChC;aAAM,IAAI,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK,cAAc,EAAE;YAC7D,mBAAmB;YACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACvC,IAAI,MAAM,IAAI,SAAS;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;SAC7C;IACH,CAAC;CACF"}