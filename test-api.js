const axios = require('axios');
require('dotenv').config();

async function testAPI() {
  try {
    console.log('제주도 주유소 가격 API 테스트 중...');
    
    const response = await axios.get('http://api.jejuits.go.kr/api/infoGasPriceList', {
      params: {
        code: '860665'
      },
      timeout: 30000
    });

    console.log('API 응답 상태:', response.status);
    console.log('API 응답 결과:', response.data.result);
    console.log('주유소 개수:', response.data.info_cnt);
    
    if (response.data.info && response.data.info.length > 0) {
      console.log('첫 번째 주유소 데이터 샘플:');
      console.log(JSON.stringify(response.data.info[0], null, 2));
    }
    
  } catch (error) {
    console.error('API 테스트 실패:', error.message);
  }
}

testAPI();
