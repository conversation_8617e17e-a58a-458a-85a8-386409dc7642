# 제주도 주유소 가격 수집 스케줄러

제주도 주유소 가격 정보를 정기적으로 수집하여 Supabase 데이터베이스에 저장하는 Node.js 애플리케이션입니다.

## 기능

- 제주도 주유소 가격 API에서 실시간 가격 정보 수집
- Supabase PostgreSQL 데이터베이스에 자동 저장
- opinet_id 기준으로 중복 데이터 방지 (upsert)
- 스케줄링을 통한 자동 실행
- 수동 실행 옵션

## 설치 및 설정

### 1. 의존성 설치
```bash
npm install
```

### 2. 환경변수 설정
`.env` 파일에서 다음 값들을 설정하세요:

```env
# Supabase 설정
SUPABASE_URL=https://effgsrxotxfhpiczalrq.supabase.co
SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=YOUR_SUPABASE_SERVICE_ROLE_KEY

# 스케줄러 설정 (cron 표현식)
SCHEDULE_CRON=0 */6 * * *  # 6시간마다 실행
```

**중요**: Supabase 키는 실제 프로젝트의 키로 교체해야 합니다.

## 사용법

### Cafe24 호스팅 (프로덕션)
```bash
npm start  # web.js 실행
```

### 로컬 개발 환경

#### 스케줄러 모드 (지속 실행)
```bash
npm run local
```

#### 한 번만 실행
```bash
node index.js --run-once
```

#### 시작 시 즉시 실행 후 스케줄러 시작
```bash
node index.js --run-on-start
```

## 스케줄 설정

`SCHEDULE_CRON` 환경변수로 실행 주기를 설정할 수 있습니다:

- `5 2,8,14,20 * * *`: 새벽 2시 5분, 오전 8시 5분, 오후 2시 5분, 오후 8시 5분 (현재 설정)
- `0 */6 * * *`: 6시간마다
- `0 */1 * * *`: 1시간마다
- `0 0 * * *`: 매일 자정
- `5 2 * * *`: 매일 새벽 2시 5분에만 실행

## 데이터베이스 스키마

gas_prices 테이블 구조:
- `id`: 기본키 (integer)
- `opinet_id`: 주유소 고유 ID (string)
- `gasoline_price`: 휘발유 가격 (integer)
- `premium_gasoline_price`: 고급휘발유 가격 (integer)
- `diesel_price`: 경유 가격 (integer)
- `lpg_price`: LPG 가격 (integer)
- `price_date`: 가격 기준일 (date)
- `api_raw_data`: 원본 API 응답 (text)
- `fetched_at`: 수집 시간 (timestamp)
- `created_at`: 생성 시간 (timestamp)
- `updated_at`: 수정 시간 (timestamp)

## 로그

애플리케이션은 다음과 같은 로그를 출력합니다:
- API 호출 상태
- 데이터 처리 진행상황
- 에러 메시지
- 스케줄 실행 정보

## 에러 처리

- API 호출 실패 시 재시도 없이 에러 로그 출력
- 데이터베이스 연결 실패 시 프로그램 종료
- 스케줄 실행 중 에러 발생 시 다음 스케줄까지 대기

## 개발 및 디버깅

개발 모드로 실행:
```bash
npm run dev
```

## Cafe24 호스팅 배포

### 1. 파일 구조
- `web.js`: Cafe24에서 요구하는 메인 실행 파일
- `index.js`: 로컬 개발용 실행 파일
- 포트 8001 사용 (Cafe24 기본 포트)

### 2. 배포 과정
1. Git 저장소 초기화
2. Cafe24에서 Public Key 등록
3. 앱 생성 및 Key 할당
4. Git Push를 통한 배포

### 3. API 엔드포인트 (Cafe24 호스팅 시)
- `GET /`: 기본 상태 정보
- `GET /status`: 스케줄러 상태 확인
- `POST /run`: 수동 실행

## 주의사항

1. Supabase 키는 보안상 중요하므로 `.env` 파일을 버전 관리에 포함하지 마세요.
2. API 호출 제한이 있을 수 있으므로 너무 자주 실행하지 마세요.
3. 데이터베이스 용량을 고려하여 오래된 데이터 정리를 고려하세요.
4. Cafe24 호스팅 시 web.js가 메인 파일로 실행됩니다.
