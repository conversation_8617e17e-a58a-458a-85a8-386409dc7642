{"name": "@supabase/gotrue-js", "version": "1.24.0", "description": "Isomorphic GoTrue client", "keywords": ["gotrue", "supabase"], "homepage": "https://github.com/supabase/gotrue-js", "bugs": "https://github.com/supabase/gotrue-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "repository": "supabase/gotrue-js", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist docs", "coverage": "echo \"run npm test\"", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "genversion src/lib/version.ts --es6 && run-s clean format build:* && run-s lint", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "lint": "eslint ./src/**/* test/**/*.test.ts", "test": "run-s test:clean test:infra test:suite test:clean", "test:suite": "jest --runInBand", "test:infra": "cd infra && docker-compose down && docker-compose pull && docker-compose up -d && sleep 30", "test:clean": "cd infra && docker-compose down --remove-orphans", "v1:docs": "typedoc src/index.ts  --out docs/v1", "v1:docs:json": "typedoc src/index.ts --json docs/v1/spec.json", "v2:docs": "typedoc src/index.ts --out docs/v2", "v2:docs:json": "typedoc src/index.ts --json docs/spec.json --out docs/v2"}, "dependencies": {"cross-fetch": "^3.0.6"}, "devDependencies": {"@types/faker": "^5.1.6", "@types/jest": "^26.0.13", "@types/jsonwebtoken": "^8.5.6", "@types/node": "^14.14.7", "@types/node-fetch": "^2.5.7", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.4.1", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "faker": "^5.3.1", "genversion": "^3.0.1", "jest": "^27.3.1", "jsonwebtoken": "^8.5.1", "npm-run-all": "^4.1.5", "prettier": "2.4.1", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "ts-jest": "^27.0.7", "typedoc": "^0.22.7", "typescript": "^4.0.2"}}