const GasPriceScheduler = require('./src/scheduler/gasPriceScheduler');

// 로컬 개발환경에서는 .env 파일 사용
try {
  require('dotenv').config();
} catch (error) {
  console.log('dotenv 로드 실패 (Cafe24 환경에서는 정상)');
}

// 전역 에러 핸들러
process.on('uncaughtException', (error) => {
  console.error('처리되지 않은 예외:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('처리되지 않은 Promise 거부:', reason);
  process.exit(1);
});

// 애플리케이션 시작
async function startApplication() {
  try {
    console.log('=== 제주도 주유소 가격 수집 스케줄러 시작 ===');
    console.log(`Node.js 버전: ${process.version}`);
    console.log(`시작 시간: ${new Date().toISOString()}`);
    
    // 설정 확인 (환경변수 또는 설정 파일)
    let config;
    try {
      config = {
        supabaseUrl: process.env.SUPABASE_URL,
        jejuApiUrl: process.env.JEJU_GAS_API_URL,
        jejuApiCode: process.env.JEJU_GAS_API_CODE
      };
    } catch (error) {
      config = require('./src/config/config');
    }

    if (!config.supabaseUrl || !config.jejuApiUrl || !config.jejuApiCode) {
      console.error('필수 설정값이 누락되었습니다. .env 파일 또는 src/config/config.js 파일을 확인하세요.');
      console.error('필요한 설정:', {
        supabaseUrl: !!config.supabaseUrl,
        jejuApiUrl: !!config.jejuApiUrl,
        jejuApiCode: !!config.jejuApiCode
      });
      process.exit(1);
    }
    
    // 스케줄러 인스턴스 생성
    const scheduler = new GasPriceScheduler();
    
    // 명령행 인수 확인
    const args = process.argv.slice(2);
    
    if (args.includes('--run-once') || args.includes('-o')) {
      // 한 번만 실행하고 종료
      console.log('한 번만 실행 모드');
      await scheduler.runOnce();
      console.log('작업 완료. 프로그램을 종료합니다.');
      process.exit(0);
    } else {
      // 스케줄러 모드로 실행
      console.log('스케줄러 모드로 실행');
      
      // 시작 시 한 번 실행 (선택사항)
      if (args.includes('--run-on-start') || args.includes('-s')) {
        console.log('시작 시 즉시 한 번 실행합니다...');
        try {
          await scheduler.runOnce();
        } catch (error) {
          console.error('시작 시 실행 실패:', error.message);
        }
      }
      
      // 스케줄러 시작
      scheduler.start();
      
      // 상태 확인 명령어 안내
      console.log('\n사용 가능한 명령어:');
      console.log('- Ctrl+C: 프로그램 종료');
      console.log('- 상태 확인: scheduler.getStatus()');
      console.log('- 수동 실행: scheduler.runOnce()');
      
      // 프로그램이 종료되지 않도록 유지
      process.stdin.resume();
    }
    
  } catch (error) {
    console.error('애플리케이션 시작 실패:', error);
    process.exit(1);
  }
}

// 우아한 종료 처리
process.on('SIGINT', () => {
  console.log('\n프로그램 종료 신호를 받았습니다. 안전하게 종료합니다...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n프로그램 종료 신호를 받았습니다. 안전하게 종료합니다...');
  process.exit(0);
});

// 애플리케이션 시작
startApplication();
