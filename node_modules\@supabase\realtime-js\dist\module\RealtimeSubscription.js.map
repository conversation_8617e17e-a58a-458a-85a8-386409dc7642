{"version": 3, "file": "RealtimeSubscription.js", "sourceRoot": "", "sources": ["../../src/RealtimeSubscription.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAA;AAChE,OAAO,IAAI,MAAM,YAAY,CAAA;AAE7B,OAAO,KAAK,MAAM,aAAa,CAAA;AAE/B,MAAM,CAAC,OAAO,OAAO,oBAAoB;IASvC,YACS,KAAa,EACb,SAAqC,EAAE,EACvC,MAAsB;QAFtB,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAiC;QACvC,WAAM,GAAN,MAAM,CAAgB;QAX/B,aAAQ,GAAU,EAAE,CAAA;QAEpB,UAAK,GAAG,cAAc,CAAC,MAAM,CAAA;QAC7B,eAAU,GAAG,KAAK,CAAA;QAGlB,eAAU,GAAW,EAAE,CAAA;QAOrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CACtB,IAAI,EACJ,cAAc,CAAC,IAAI,EACnB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CACb,CAAA;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,CAC1B,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC7B,CAAA;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAA;YAClC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAe,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACtB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACnE,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAA;YAClC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,EAAE;YAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACvC,OAAM;aACP;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,CAAA;YACzD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAA;YACnC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QACpC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACrB,OAAM;aACP;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC1E,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAA;YACnC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QACpC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,OAAY,EAAE,GAAW,EAAE,EAAE;YAC1D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAA;SACd;IACH,CAAC;IAED,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;QAC9B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,MAAM,sGAAsG,CAAA;SAC7G;aAAM;YACL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,OAAO,IAAI,CAAC,QAAQ,CAAA;SACrB;IACH,CAAC;IAED,OAAO,CAAC,QAAkB;QACxB,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IACzC,CAAC;IAED,OAAO,CAAC,QAAkB;QACxB,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,MAAc,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IACrE,CAAC;IAED,EAAE,CAAC,KAAa,EAAE,QAAkB;QAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;IACzC,CAAC;IAED,GAAG,CAAC,KAAa;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,CAAA;IACtE,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAA;IACrD,CAAC;IAED,IAAI,CAAC,KAAqB,EAAE,OAAY,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO;QAC9D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,kBAAkB,KAAK,SAAS,IAAI,CAAC,KAAK,iEAAiE,CAAA;SAClH;QACD,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAClB,SAAS,CAAC,IAAI,EAAE,CAAA;SACjB;aAAM;YACL,SAAS,CAAC,YAAY,EAAE,CAAA;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;SAChC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,iBAAiB,CAAC,OAAmC;QACnD,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;QAChC,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAA;QACnC,IAAI,OAAO,GAAG,GAAG,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;YACjD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QAC7D,CAAC,CAAA;QACD,yEAAyE;QACzE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;QAEvB,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC,CAAA;QACjE,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;QAC5E,SAAS,CAAC,IAAI,EAAE,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACnB,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;SAC5B;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,KAAa,EAAE,OAAY,EAAE,GAAY;QACjD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,QAAQ,CAAC,KAAa;QACpB,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAA;IAC7B,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAA;IAC1B,CAAC;IAED,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;QAC3B,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACpB,OAAM;SACP;QACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACtC,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAA;QACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/B,CAAC;IAED,OAAO,CAAC,KAAa,EAAE,OAAa,EAAE,GAAY;QAChD,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,cAAc,CAAA;QAClD,IAAI,MAAM,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAClD,IAAI,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,EAAE,EAAE;YAC/D,OAAM;SACP;QACD,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA;QACxD,IAAI,OAAO,IAAI,CAAC,cAAc,EAAE;YAC9B,MAAM,6EAA6E,CAAA;SACpF;QAED,IAAI,CAAC,QAAQ;aACV,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACf,oDAAoD;YACpD,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;gBACtB,OAAO,KAAK,MAAK,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAA,CAAA;aAC/B;iBAAM;gBACL,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAA;aAC5B;QACH,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;IACtD,CAAC;IAED,cAAc,CAAC,GAAW;QACxB,OAAO,cAAc,GAAG,EAAE,CAAA;IAC5B,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,MAAM,CAAA;IAC7C,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,OAAO,CAAA;IAC9C,CAAC;IACD,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,MAAM,CAAA;IAC7C,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,OAAO,CAAA;IAC9C,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC,OAAO,CAAA;IAC9C,CAAC;CACF"}