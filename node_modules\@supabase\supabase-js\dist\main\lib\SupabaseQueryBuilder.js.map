{"version": 3, "file": "SupabaseQueryBuilder.js", "sourceRoot": "", "sources": ["../../../src/lib/SupabaseQueryBuilder.ts"], "names": [], "mappings": ";;;AAAA,yDAA8D;AAC9D,qEAAiE;AAIjE,MAAa,oBAAwB,SAAQ,oCAAwB;IAOnE,YACE,GAAW,EACX,EACE,OAAO,GAAG,EAAE,EACZ,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK,EACL,kBAAkB,GAQnB;QAED,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAA;QAxBpD,kBAAa,GAAkC,IAAI,CAAA;QA0BzD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;IAED;;;;OAIG;IACH,EAAE,CACA,KAAyB,EACzB,QAAuD;QAEvD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE;YACjC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;SACzB;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,aAAa,GAAG,IAAI,+CAAsB,CAC7C,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,MAAM,CACZ,CAAA;SACF;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;CACF;AAvDD,oDAuDC"}