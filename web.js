#!/usr/bin/env node

/**
 * Cafe24 Node.js 호스팅용 메인 파일
 * Cafe24에서는 web.js를 기본 실행 파일로 인식합니다.
 */

const GasPriceScheduler = require('./src/scheduler/gasPriceScheduler');
const http = require('http');
const express = require('express');
const config = require('./src/config/config');

// Express 앱 생성 (Cafe24 호스팅에서 HTTP 서버 필요)
const app = express();

// 포트 설정
const port = config.port || 8001;

// 기본 라우트 설정
app.get('/', (req, res) => {
  res.json({
    message: '제주도 주유소 가격 수집 스케줄러',
    status: 'running',
    timestamp: new Date().toISOString(),
    scheduler: scheduler ? scheduler.getStatus() : 'not initialized'
  });
});

// 상태 확인 API
app.get('/status', (req, res) => {
  if (scheduler) {
    res.json(scheduler.getStatus());
  } else {
    res.status(500).json({ error: 'Scheduler not initialized' });
  }
});

// 수동 실행 API
app.post('/run', async (req, res) => {
  if (!scheduler) {
    return res.status(500).json({ error: 'Scheduler not initialized' });
  }
  
  try {
    const result = await scheduler.runOnce();
    res.json({
      success: true,
      message: '수동 실행 완료',
      result: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '수동 실행 실패',
      error: error.message
    });
  }
});

// 전역 에러 핸들러
process.on('uncaughtException', (error) => {
  console.error('처리되지 않은 예외:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('처리되지 않은 Promise 거부:', reason);
});

// 스케줄러 인스턴스
let scheduler = null;

// 서버 시작
async function startServer() {
  try {
    console.log('=== Cafe24 제주도 주유소 가격 수집 스케줄러 시작 ===');
    console.log(`Node.js 버전: ${process.version}`);
    console.log(`시작 시간: ${new Date().toISOString()}`);
    console.log(`포트: ${port}`);
    
    // 설정 확인
    if (!config.supabaseUrl || !config.jejuApiUrl || !config.jejuApiCode) {
      console.error('필수 설정값이 누락되었습니다. src/config/config.js 파일을 확인하세요.');
      console.error('필요한 설정:', {
        supabaseUrl: !!config.supabaseUrl,
        jejuApiUrl: !!config.jejuApiUrl,
        jejuApiCode: !!config.jejuApiCode
      });
      return;
    }

    if (config.supabaseKey === 'YOUR_SUPABASE_KEY_HERE') {
      console.error('Supabase API 키가 설정되지 않았습니다. src/config/config.js 파일에서 실제 키로 교체하세요.');
      return;
    }
    
    // HTTP 서버 시작
    const server = http.createServer(app);
    server.listen(port, () => {
      console.log(`HTTP 서버가 포트 ${port}에서 실행 중입니다.`);
    });
    
    // 스케줄러 초기화 및 시작
    scheduler = new GasPriceScheduler();
    
    // 시작 시 한 번 실행
    console.log('시작 시 즉시 한 번 실행합니다...');
    try {
      await scheduler.runOnce();
      console.log('시작 시 실행 완료');
    } catch (error) {
      console.error('시작 시 실행 실패:', error.message);
    }
    
    // 스케줄러 시작
    scheduler.start();
    
    console.log('스케줄러가 성공적으로 시작되었습니다.');
    console.log('API 엔드포인트:');
    console.log('- GET /: 기본 상태 정보');
    console.log('- GET /status: 스케줄러 상태');
    console.log('- POST /run: 수동 실행');
    
  } catch (error) {
    console.error('서버 시작 실패:', error);
  }
}

// 우아한 종료 처리
process.on('SIGINT', () => {
  console.log('\n프로그램 종료 신호를 받았습니다. 안전하게 종료합니다...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n프로그램 종료 신호를 받았습니다. 안전하게 종료합니다...');
  process.exit(0);
});

// 서버 시작
startServer();
