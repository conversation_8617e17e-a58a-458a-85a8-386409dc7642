const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 클라이언트 생성
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  throw new Error('SUPABASE_URL 환경변수가 설정되지 않았습니다.');
}

if (!supabaseKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY 또는 SUPABASE_ANON_KEY 환경변수가 설정되지 않았습니다.');
}

const supabase = createClient(supabaseUrl, supabaseKey);

module.exports = {
  supabase
};
