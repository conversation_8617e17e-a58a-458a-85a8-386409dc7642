const { createClient } = require('@supabase/supabase-js');
const config = require('./config');

// Supabase 클라이언트 생성
const supabaseUrl = config.supabaseUrl;
const supabaseKey = config.supabaseKey;

if (!supabaseUrl) {
  throw new Error('SUPABASE_URL이 설정되지 않았습니다. src/config/config.js 파일을 확인하세요.');
}

if (!supabaseKey || supabaseKey === 'YOUR_SUPABASE_KEY_HERE') {
  throw new Error('SUPABASE_KEY가 설정되지 않았습니다. src/config/config.js 파일에서 실제 키로 교체하세요.');
}

const supabase = createClient(supabaseUrl, supabaseKey);

module.exports = {
  supabase
};
