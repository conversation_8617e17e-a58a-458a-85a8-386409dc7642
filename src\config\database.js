const { createClient } = require('@supabase/supabase-js');

// Cafe24 호스팅에서는 .env 파일 사용 불가능
// 환경변수 대신 설정 파일 사용
let config;
try {
  // 로컬 개발환경에서는 .env 파일 사용
  require('dotenv').config();
  config = {
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY,
    jejuApiUrl: process.env.JEJU_GAS_API_URL,
    jejuApiCode: process.env.JEJU_GAS_API_CODE,
    scheduleCron: process.env.SCHEDULE_CRON
  };
} catch (error) {
  // Cafe24 호스팅 환경에서는 직접 설정값 사용
  config = require('./config');
}

// Supabase 클라이언트 생성
const supabaseUrl = config.supabaseUrl;
const supabaseKey = config.supabaseKey;

if (!supabaseUrl) {
  throw new Error('SUPABASE_URL이 설정되지 않았습니다. config.js 파일을 확인하세요.');
}

if (!supabaseKey) {
  throw new Error('SUPABASE_KEY가 설정되지 않았습니다. config.js 파일을 확인하세요.');
}

const supabase = createClient(supabaseUrl, supabaseKey);

module.exports = {
  supabase
};
