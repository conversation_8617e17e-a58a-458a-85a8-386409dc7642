// helpers.ts
export function uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}
export function stripTrailingSlash(url) {
    return url.replace(/\/$/, '');
}
export const isBrowser = () => typeof window !== 'undefined';
//# sourceMappingURL=helpers.js.map