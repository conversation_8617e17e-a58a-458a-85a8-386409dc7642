{"name": "@supabase/supabase-js", "version": "1.35.7", "description": "Isomorphic Javascript client for Supabase", "keywords": ["javascript", "typescript", "supabase"], "homepage": "https://github.com/supabase/supabase-js", "bugs": "https://github.com/supabase/supabase-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "supabase/supabase-js", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist docs", "coverage": "jest --runInBand --coverage", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack", "types-generate": "dts-gen -m '@supabase/supabase-js' -s", "test": "jest --runInBand", "test:db": "cd infra/db && docker-compose down && docker-compose up -d && sleep 5", "test:watch": "jest --watch --verbose false --silent false", "test:clean": "cd infra/db && docker-compose down", "docs": "typedoc --entryPoints src/index.ts --out docs --includes src/**/*.ts", "docs:json": "typedoc --entryPoints src/index.ts --out docs --includes src/**/*.ts --json docs/spec.json", "v1:docs": "typedoc src/index.ts --includes src/**/*.ts --out docs/v1", "v1:docs:json": "typedoc --includes  src/**/*.ts --json docs/v1/spec.json --excludeExternals src/index.ts", "v2:docs": "typedoc src/index.ts --includes src/**/*.ts --out docs/v2", "v2:docs:json": "typedoc --includes src/**/*.ts --json docs/v2/spec.json --excludeExternals src/index.ts"}, "dependencies": {"@supabase/functions-js": "^1.3.4", "@supabase/gotrue-js": "^1.22.21", "@supabase/postgrest-js": "^0.37.4", "@supabase/realtime-js": "^1.7.5", "@supabase/storage-js": "^1.7.2"}, "devDependencies": {"@types/jest": "^26.0.13", "husky": "^4.3.0", "jest": "^26.4.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "ts-jest": "^26.3.0", "ts-loader": "^8.0.11", "typedoc": "^0.22.15", "typescript": "^4.5.5", "webpack": "^5.69.1", "webpack-cli": "^4.9.2"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "jsdelivr": "dist/umd/supabase.js", "unpkg": "dist/umd/supabase.js"}