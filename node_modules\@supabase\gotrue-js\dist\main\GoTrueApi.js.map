{"version": 3, "file": "GoTrueApi.js", "sourceRoot": "", "sources": ["../../src/GoTrueApi.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,uCAA2D;AAW3D,+CAAgD;AAChD,2CAA2D;AAC3D,2CAAuD;AAGvD,MAAqB,SAAS;IAQ5B,YAAY,EACV,GAAG,GAAG,EAAE,EACR,OAAO,GAAG,EAAE,EACZ,aAAa,EACb,KAAK,GAQN;QACC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,aAAa,mCAAQ,0BAAc,GAAK,aAAa,CAAE,CAAA;QAC5D,IAAI,CAAC,KAAK,GAAG,IAAA,sBAAY,EAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,GAAW;QACvC,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;QACnC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,GAAG,EAAE,CAAA;QAC1C,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,UAAU;;QAChB,OAAO,MAAA,IAAI,CAAC,aAAa,CAAC,IAAI,mCAAI,EAAE,CAAA;IACtC,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CACf,QAAkB,EAClB,OAIC;QAED,MAAM,SAAS,GAAa,CAAC,YAAY,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACxE,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE;YACvB,SAAS,CAAC,IAAI,CAAC,eAAe,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;SACxE;QACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;YACnB,SAAS,CAAC,IAAI,CAAC,UAAU,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;SAC/D;QACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE;YACxB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;YACtD,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,CAAA;SAC3B;QACD,OAAO,GAAG,IAAI,CAAC,GAAG,cAAc,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;IACvD,CAAC;IAED;;;;;;;;;;OAUG;IACG,eAAe,CACnB,KAAa,EACb,QAAgB,EAChB,UAII,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,IAAI,WAAW,GAAG,EAAE,CAAA;gBACpB,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,WAAW,GAAG,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;iBACvE;gBACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,EAClC;oBACE,KAAK;oBACL,QAAQ;oBACR,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE;iBAC9D,EACD,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,eAAe,CACnB,KAAa,EACb,QAAgB,EAChB,UAGI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,IAAI,WAAW,GAAG,sBAAsB,CAAA;gBACxC,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,WAAW,IAAI,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;iBACxE;gBACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,WAAW,EAAE,EACjC,EAAE,KAAK,EAAE,QAAQ,EAAE,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,EAClF,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,eAAe,CACnB,KAAa,EACb,QAAgB,EAChB,UAGI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,EACpB;oBACE,KAAK;oBACL,QAAQ;oBACR,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE;iBAC9D,EACD,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,eAAe,CACnB,KAAa,EACb,QAAgB,EAChB,UAEI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,WAAW,GAAG,sBAAsB,CAAA;gBAC1C,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,WAAW,EAAE,EACjC,EAAE,KAAK,EAAE,QAAQ,EAAE,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,EAClF,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,uBAAuB,CAAC,EAC5B,QAAQ,EACR,KAAK,EACL,SAAS,EACT,MAAM,EACN,QAAQ,GACiB;;YACzB,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,WAAW,GAAG,sBAAsB,CAAA;gBAC1C,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,WAAW,EAAE,EACjC,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,EAChD,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,kBAAkB,CACtB,KAAa,EACb,UAII,EAAE;;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,IAAI,WAAW,GAAG,EAAE,CAAA;gBACpB,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,WAAW,IAAI,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;iBACxE;gBAED,MAAM,gBAAgB,GAAG,MAAA,OAAO,CAAC,gBAAgB,mCAAI,IAAI,CAAA;gBACzD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,OAAO,WAAW,EAAE,EAC/B;oBACE,KAAK;oBACL,WAAW,EAAE,gBAAgB;oBAC7B,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE;iBAC9D,EACD,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;;KACF;IAED;;;;;OAKG;IACG,aAAa,CACjB,KAAa,EACb,UAGI,EAAE;;;YAEN,IAAI;gBACF,MAAM,gBAAgB,GAAG,MAAA,OAAO,CAAC,gBAAgB,mCAAI,IAAI,CAAA;gBACzD,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,MAAM,EACjB;oBACE,KAAK;oBACL,WAAW,EAAE,gBAAgB;oBAC7B,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE;iBAC9D,EACD,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;;KACF;IAED;;;OAGG;IACG,OAAO,CAAC,GAAW;;YACvB,IAAI;gBACF,MAAM,IAAA,YAAI,EACR,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,EACpB,EAAE,EACF,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAClE,CAAA;gBACD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACvB;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAChC;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,eAAe,CACnB,KAAa,EACb,KAAa,EACb,UAEI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,EACpB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,EAC9D,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,SAAS,CACb,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAG,KAAK,EAAmB,EACtD,UAEI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,EACpB,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,EAC9D,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,iBAAiB,CACrB,KAAa,EACb,UAGI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,IAAI,WAAW,GAAG,EAAE,CAAA;gBACpB,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,WAAW,IAAI,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;iBACxE;gBACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,EAClC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAC7B,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,qBAAqB,CACzB,KAAa,EACb,UAGI,EAAE;;YAEN,IAAI;gBACF,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBACnC,IAAI,WAAW,GAAG,EAAE,CAAA;gBACpB,IAAI,OAAO,CAAC,UAAU,EAAE;oBACtB,WAAW,IAAI,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;iBACxE;gBACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,WAAW,EAAE,EACnC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,EACxE,EAAE,OAAO,EAAE,CACZ,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;OAGG;IACG,kBAAkB,CACtB,YAAoB;;YAEpB,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,YAAI,EAC1B,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,iCAAiC,EAC5C,EAAE,aAAa,EAAE,YAAY,EAAE,EAC/B,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,MAAM,OAAO,qBAAQ,IAAI,CAAE,CAAA;gBAC3B,IAAI,OAAO,CAAC,UAAU;oBAAE,OAAO,CAAC,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACH,aAAa,CAAC,GAAQ,EAAE,GAAQ;QAC9B,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE;YACzB,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAC9B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;SAC1C;QACD,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAEnC,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QAClD,IAAI,KAAK,KAAK,WAAW,EAAE;YACzB,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;YACtD,IAAA,oBAAU,EACR,GAAG,EACH,GAAG,EACH;gBACE,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE;gBACpD,EAAE,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,aAAa,EAAE;aACvD,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;;gBAAC,OAAA,CAAC;oBAChB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,GAAG,EAAE;oBACzC,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;oBACjC,MAAM,EAAE,MAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,mCAAI,CAAC;oBACxC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;oBAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;iBACtC,CAAC,CAAA;aAAA,CAAC,CACJ,CAAA;SACF;QACD,IAAI,KAAK,KAAK,YAAY,EAAE;YAC1B,IAAA,oBAAU,EACR,GAAG,EACH,GAAG,EACH,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAC9C,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE;gBACnC,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,CAAC,CAAC;aACX,CAAC,CAAC,CACJ,CAAA;SACF;QACD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE,UAAU,GAAG,GAAG,EAA2B;QAChF,IAAA,oBAAU,EACR,GAAG,EACH,GAAG,EACH,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAC9C,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE;YACnC,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,CAAC,CAAC;SACX,CAAC,CAAC,CACJ,CAAA;QACD,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CAAC,GAAQ,EAAE,GAAQ;QACpC,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE;YACzB,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAC9B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;SAC1C;QACD,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAEnC,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QAClD,IAAI,KAAK,KAAK,WAAW,EAAE;YACzB,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;YACtD,OAAO,IAAA,yBAAe,EACpB,GAAG,EACH,GAAG,EACH;gBACE,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE;gBACpD,EAAE,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,aAAa,EAAE;aACvD,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;;gBAAC,OAAA,CAAC;oBAChB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,GAAG,EAAE;oBACzC,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;oBACjC,MAAM,EAAE,MAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,mCAAI,CAAC;oBACxC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;oBAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;iBACtC,CAAC,CAAA;aAAA,CAAC,CACJ,CAAA;SACF;QACD,IAAI,KAAK,KAAK,YAAY,EAAE;YAC1B,OAAO,IAAA,yBAAe,EACpB,GAAG,EACH,GAAG,EACH,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAC9C,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE;gBACnC,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,CAAC,CAAC;aACX,CAAC,CAAC,CACJ,CAAA;SACF;QACD,OAAO,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;IACpC,CAAC;IAED;;;;;;;OAOG;IACG,YAAY,CAChB,IAMsB,EACtB,KAAa,EACb,UAII,EAAE;;YAEN,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,YAAI,EAC1B,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,sBAAsB,EACjC;oBACE,IAAI;oBACJ,KAAK;oBACL,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,WAAW,EAAE,OAAO,CAAC,UAAU;iBAChC,EACD,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED,iBAAiB;IAEjB;;;;;;OAMG;IACG,UAAU,CACd,UAA+B;;YAI/B,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,YAAI,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE,UAAU,EAAE;oBAC9E,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;QACH,CAAC;KAAA;IAED;;;;OAIG;IACG,SAAS;;YACb,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE;oBACjE,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,WAAW,CACf,GAAW;;YAEX,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,EAAE,EAAE;oBACxE,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;OAGG;IACG,eAAe,CACnB,GAAQ,EACR,GAAS;;YAOT,IAAI;gBACF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;oBAChB,MAAM,IAAI,KAAK,CACb,iGAAiG,CAClG,CAAA;iBACF;gBAED,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAA;gBACrE,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAA;gBAEvE,IAAI,CAAC,YAAY,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;iBACpC;gBAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;gBACtE,IAAI,YAAY,EAAE;oBAChB,IAAI,CAAC,aAAa;wBAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;oBACrE,IAAI,CAAC,GAAG;wBACN,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAA;oBAC1F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;oBACpE,IAAI,KAAK,EAAE;wBACT,MAAM,KAAK,CAAA;qBACZ;yBAAM,IAAI,IAAI,EAAE;wBACf,IAAA,oBAAU,EACR,GAAG,EACH,GAAG,EACH;4BACE,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;4BACjD,EAAE,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,aAAc,EAAE;yBACrD,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;;4BAAC,OAAA,CAAC;gCAChB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,GAAG,EAAE;gCACzC,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;gCACjC,MAAM,EAAE,MAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,mCAAI,CAAC;gCACxC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;gCAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;6BACtC,CAAC,CAAA;yBAAA,CAAC,CACJ,CAAA;wBACD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;qBACnF;iBACF;gBACD,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACpE;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACrE;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,cAAc,CAClB,GAAW,EACX,UAA+B;;YAE/B,IAAI;gBACF,IAAI,CAAA,CAAC,EAAE;gBACP,MAAM,IAAI,GAAQ,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,EAAE,EAAE,UAAU,EAAE;oBACpF,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,UAAU,CACd,GAAW;;YAEX,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,cAAM,EAC5B,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,EAAE,EAChC,EAAE,EACF;oBACE,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CACF,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;QACH,CAAC;KAAA;IAED;;;;;;;;OAQG;IACG,OAAO,CACX,GAAW;;YAEX,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,EAAE;oBAC1D,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC;iBACzC,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;QACH,CAAC;KAAA;IAED;;;;OAIG;IACG,UAAU,CACd,GAAW,EACX,UAA0B;;YAE1B,IAAI;gBACF,MAAM,IAAI,GAAQ,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,EAAE,UAAU,EAAE;oBACtE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC;iBACzC,CAAC,CAAA;gBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;QACH,CAAC;KAAA;CACF;AAx0BD,4BAw0BC"}