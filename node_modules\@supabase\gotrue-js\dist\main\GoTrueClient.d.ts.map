{"version": 3, "file": "GoTrueClient.d.ts", "sourceRoot": "", "sources": ["../../src/GoTrueClient.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,aAAa,CAAA;AAkBnC,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAA;AAEnC,OAAO,KAAK,EACV,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,aAAa,EACb,eAAe,EACf,eAAe,EAEf,gBAAgB,EACjB,MAAM,aAAa,CAAA;AA+BpB,MAAM,CAAC,OAAO,OAAO,YAAY;IAC/B;;;OAGG;IACH,GAAG,EAAE,SAAS,CAAA;IACd;;OAEG;IACH,SAAS,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,CAAA;IAClC;;OAEG;IACH,SAAS,CAAC,cAAc,EAAE,OAAO,GAAG,IAAI,CAAA;IAExC,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAA;IACnC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAA;IACjC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAA;IACxC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAA;IAC3B,SAAS,CAAC,mBAAmB,EAAE,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAY;IACpE,SAAS,CAAC,iBAAiB,CAAC,EAAE,UAAU,CAAC,OAAO,UAAU,CAAC,CAAA;IAC3D,SAAS,CAAC,cAAc,EAAE,MAAM,CAAI;IAEpC;;;;;;;;;;;OAWG;gBACS,OAAO,EAAE;QACnB,GAAG,CAAC,EAAE,MAAM,CAAA;QACZ,OAAO,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SAAE,CAAA;QACnC,kBAAkB,CAAC,EAAE,OAAO,CAAA;QAC5B,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,cAAc,CAAC,EAAE,OAAO,CAAA;QACxB,YAAY,CAAC,EAAE,gBAAgB,CAAA;QAC/B,QAAQ,CAAC,EAAE,OAAO,CAAA;QAClB,aAAa,CAAC,EAAE,aAAa,CAAA;QAC7B,KAAK,CAAC,EAAE,KAAK,CAAA;KACd;IA6BD;;;;;;;;OAQG;IACG,MAAM,CACV,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,eAAe,EAC3C,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QACT,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;QACvB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB,CAAC;IA4CF;;;;;;;;;;;OAWG;IACG,MAAM,CACV,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,eAAe,EACzE,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,YAAY,CAAC,EAAE,MAAM,CAAA;QACrB,WAAW,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SAAE,CAAA;KACnC,GACL,OAAO,CAAC;QACT,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;QACvB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,QAAQ,CAAC,EAAE,QAAQ,CAAA;QACnB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACnB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB,CAAC;IAyDF;;;;;;;OAOG;IACG,SAAS,CACb,MAAM,EAAE,eAAe,EACvB,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;KACf,GACL,OAAO,CAAC;QACT,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;QACvB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB,CAAC;IAkCF;;;;OAIG;IACH,IAAI,IAAI,IAAI,GAAG,IAAI;IAInB;;OAEG;IACH,OAAO,IAAI,OAAO,GAAG,IAAI;IAIzB;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC;QAC9B,IAAI,EAAE,OAAO,GAAG,IAAI,CAAA;QACpB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB,CAAC;IAcF;;OAEG;IACG,MAAM,CACV,UAAU,EAAE,cAAc,GACzB,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAqB5E;;;;;;;;;OASG;IACG,UAAU,CAAC,MAAM,EAAE;QACvB,aAAa,EAAE,MAAM,CAAA;QACrB,YAAY,EAAE,MAAM,CAAA;KACrB,GAAG,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAEhE;;;OAGG;IACG,UAAU,CACd,aAAa,EAAE,MAAM,GACpB,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IA4E/D;;;OAGG;IACH,OAAO,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO;IAatC;;;OAGG;IACG,iBAAiB,CAAC,OAAO,CAAC,EAAE;QAChC,YAAY,CAAC,EAAE,OAAO,CAAA;KACvB,GAAG,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAmD7D;;;;;OAKG;IACG,OAAO,IAAI,OAAO,CAAC;QAAE,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAWpD;;;OAGG;IACH,iBAAiB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,KAAK,IAAI,GAAG;QACtF,IAAI,EAAE,YAAY,GAAG,IAAI,CAAA;QACzB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB;YAiBa,kBAAkB;YA0BlB,kBAAkB;IAsBhC,OAAO,CAAC,qBAAqB;YA2Bf,0BAA0B;IA+BxC;;;OAGG;IACH,OAAO,CAAC,eAAe;IAgBvB;;;OAGG;YACW,kBAAkB;YA6ClB,iBAAiB;IAmB/B,OAAO,CAAC,qBAAqB;IAI7B;;;OAGG;IACH,OAAO,CAAC,YAAY;IAmBpB,OAAO,CAAC,eAAe;YAKT,cAAc;IAO5B;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IAiB9B;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAuBhC,OAAO,CAAC,uBAAuB;CAehC"}