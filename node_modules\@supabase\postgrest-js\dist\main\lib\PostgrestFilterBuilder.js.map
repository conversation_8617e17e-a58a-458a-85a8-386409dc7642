{"version": 3, "file": "PostgrestFilterBuilder.js", "sourceRoot": "", "sources": ["../../../src/lib/PostgrestFilterBuilder.ts"], "names": [], "mappings": ";;;;;AAAA,4FAAmE;AAoDnE,MAAqB,sBAA0B,SAAQ,mCAA4B;IAAnF;;QA+KE,4CAA4C;QAC5C,OAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;QAuBlB,+CAA+C;QAC/C,OAAE,GAAG,IAAI,CAAC,WAAW,CAAA;QAcrB,2CAA2C;QAC3C,OAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAcjB,2CAA2C;QAC3C,OAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAcjB,4CAA4C;QAC5C,QAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;QAcnB,4CAA4C;QAC5C,QAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;QAcnB,iDAAiD;QACjD,QAAG,GAAG,IAAI,CAAC,aAAa,CAAA;QAoBxB,4CAA4C;QAC5C,OAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;IAwHpB,CAAC;IA/ZC;;;;;;OAMG;IACH,GAAG,CAAC,MAAe,EAAE,QAAwB,EAAE,KAAU;QACvD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,QAAQ,IAAI,KAAK,EAAE,CAAC,CAAA;QACrE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,EAAE,CAAC,OAAe,EAAE,EAAE,YAAY,KAAgC,EAAE;QAClE,MAAM,GAAG,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,KAAK,CAAA;QAC7E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,GAAG,CAAC,CAAA;QACjD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAC,MAAe,EAAE,KAAiB;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAC,MAAe,EAAE,KAAiB;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAC,MAAe,EAAE,KAAiB;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAC,MAAe,EAAE,KAAiB;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAC,MAAe,EAAE,KAAiB;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAC,MAAe,EAAE,KAAiB;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CAAC,MAAe,EAAE,OAAe;QACnC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAe,EAAE,OAAe;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,SAAS,OAAO,EAAE,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAC,MAAe,EAAE,KAAqB;QACvC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAC,MAAe,EAAE,MAAoB;QACtC,MAAM,aAAa,GAAG,MAAM;aACzB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,uCAAuC;YACvC,+DAA+D;YAC/D,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAA;;gBACpE,OAAO,GAAG,CAAC,EAAE,CAAA;QACpB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,aAAa,GAAG,CAAC,CAAA;QAClE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CAAC,MAAe,EAAE,KAAqC;QAC7D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,sEAAsE;YACtE,qCAAqC;YACrC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;SACzD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACrE;aAAM;YACL,OAAO;YACP,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACzE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,WAAW,CAAC,MAAe,EAAE,KAAqC;QAChE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;SACzD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACrE;aAAM;YACL,OAAO;YACP,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACzE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,OAAO,CAAC,MAAe,EAAE,KAAa;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,OAAO,CAAC,MAAe,EAAE,KAAa;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,QAAQ,CAAC,MAAe,EAAE,KAAa;QACrC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,QAAQ,CAAC,MAAe,EAAE,KAAa;QACrC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,aAAa,CAAC,MAAe,EAAE,KAAa;QAC1C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;OAMG;IACH,QAAQ,CAAC,MAAe,EAAE,KAA4B;QACpD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE,CAAC,CAAA;SACzD;aAAM;YACL,QAAQ;YACR,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACrE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD;;;;;;;;OAQG;IACH,UAAU,CACR,MAAe,EACf,KAAa,EACb,EACE,MAAM,EACN,IAAI,GAAG,IAAI,MAC4D,EAAE;QAE3E,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,QAAQ,GAAG,IAAI,CAAA;SAChB;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC5B,QAAQ,GAAG,IAAI,CAAA;SAChB;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC/B,QAAQ,GAAG,GAAG,CAAA;SACf;QACD,MAAM,UAAU,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAA;QAC5D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,QAAQ,MAAM,UAAU,IAAI,KAAK,EAAE,CAAC,CAAA;QACjF,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,GAAG,CAAC,MAAe,EAAE,KAAa,EAAE,EAAE,MAAM,KAA0B,EAAE;QACtE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAA;QACrE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,UAAU,IAAI,KAAK,EAAE,CAAC,CAAA;QACtE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,MAAe,EAAE,KAAa,EAAE,EAAE,MAAM,KAA0B,EAAE;QACxE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAA;QACrE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,QAAQ,UAAU,IAAI,KAAK,EAAE,CAAC,CAAA;QACxE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,MAAe,EAAE,KAAa,EAAE,EAAE,MAAM,KAA0B,EAAE;QACxE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAA;QACrE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,QAAQ,UAAU,IAAI,KAAK,EAAE,CAAC,CAAA;QACxE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI,CAAC,MAAe,EAAE,KAAa,EAAE,EAAE,MAAM,KAA0B,EAAE;QACvE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAA;QACrE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,CAAA;QACvE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,MAAe,EAAE,QAAwB,EAAE,KAAU;QAC1D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,QAAQ,IAAI,KAAK,EAAE,CAAC,CAAA;QACjE,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAA8B;QAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACjC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,EAAE,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QAC5D,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAhaD,yCAgaC"}