/**
 * Cafe24 호스팅용 설정 파일
 * .env 파일을 사용할 수 없는 환경에서 사용
 */

module.exports = {
  // Supabase 설정
  supabaseUrl: 'https://effgsrxotxfhpiczalrq.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVmZmdzcnhvdHhmaHBpY3phbHJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNTk1MTEsImV4cCI6MjA2MzgzNTUxMX0.ZVt17MVMNHl63vPQOV5wxgMUBBZmJzuzFIwFAujD0AU', // 실제 키로 교체 필요
  
  // 제주도 주유소 API 설정
  jejuApiUrl: 'http://api.jejuits.go.kr/api/infoGasPriceList',
  jejuApiCode: '860665',
  
  // 스케줄러 설정 (새벽 2시 5분 + 6시간마다)
  scheduleCron: '5 2,8,14,20 * * *',
  
  // 서버 포트 (Cafe24 기본 포트)
  port: 8001
};
