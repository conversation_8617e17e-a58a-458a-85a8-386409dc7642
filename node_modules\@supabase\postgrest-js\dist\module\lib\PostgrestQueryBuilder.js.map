{"version": 3, "file": "PostgrestQueryBuilder.js", "sourceRoot": "", "sources": ["../../../src/lib/PostgrestQueryBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,gBAAgB,EAAE,MAAM,SAAS,CAAA;AACjD,OAAO,sBAAsB,MAAM,0BAA0B,CAAA;AAE7D,MAAM,CAAC,OAAO,OAAO,qBAAyB,SAAQ,gBAAmB;IACvE,YACE,GAAW,EACX,EACE,OAAO,GAAG,EAAE,EACZ,MAAM,EACN,KAAK,EACL,kBAAkB,MAMhB,EAAE;QAEN,KAAK,CAAE,EAAE,KAAK,EAAE,kBAAkB,EAAqC,CAAC,CAAA;QACxE,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;QACvB,IAAI,CAAC,OAAO,qBAAQ,OAAO,CAAE,CAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CACJ,OAAO,GAAG,GAAG,EACb,EACE,IAAI,GAAG,KAAK,EACZ,KAAK,GAAG,IAAI,MAIV,EAAE;QAEN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,OAAO;aAC3B,KAAK,CAAC,EAAE,CAAC;aACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,SAAS,KAAK,EAAE,CAAA;SAC1C;QACD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;SACrB;QACD,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IA4BD,MAAM,CACJ,MAAiC,EACjC,EACE,MAAM,GAAG,KAAK,EACd,UAAU,EACV,SAAS,GAAG,gBAAgB,EAC5B,KAAK,GAAG,IAAI,MAMV,EAAE;QAEN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,MAAM,cAAc,GAAG,CAAC,UAAU,SAAS,EAAE,CAAC,CAAA;QAC9C,IAAI,MAAM;YAAE,cAAc,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAE9D,IAAI,MAAM,IAAI,UAAU,KAAK,SAAS;YAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QAC5F,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA;QAClB,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC,CAAA;YACrF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC,CAAA;gBAC1E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aAC9D;SACF;QAED,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CACJ,MAAiC,EACjC,EACE,UAAU,EACV,SAAS,GAAG,gBAAgB,EAC5B,KAAK,GAAG,IAAI,EACZ,gBAAgB,GAAG,KAAK,MAMtB,EAAE;QAEN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,MAAM,cAAc,GAAG;YACrB,cAAc,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,aAAa;YAChE,UAAU,SAAS,EAAE;SACtB,CAAA;QAED,IAAI,UAAU,KAAK,SAAS;YAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QAClF,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA;QAClB,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CACJ,MAAkB,EAClB,EACE,SAAS,GAAG,gBAAgB,EAC5B,KAAK,GAAG,IAAI,MAIV,EAAE;QAEN,IAAI,CAAC,MAAM,GAAG,OAAO,CAAA;QACrB,MAAM,cAAc,GAAG,CAAC,UAAU,SAAS,EAAE,CAAC,CAAA;QAC9C,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA;QAClB,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,EACL,SAAS,GAAG,gBAAgB,EAC5B,KAAK,GAAG,IAAI,MAIV,EAAE;QACJ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;QACtB,MAAM,cAAc,GAAG,CAAC,UAAU,SAAS,EAAE,CAAC,CAAA;QAC9C,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;CACF"}