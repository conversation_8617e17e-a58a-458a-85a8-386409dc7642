!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.supabase=t():e.supabase=t()}(self,(()=>(()=>{var e,t,r,s,n={248:(e,t,r)=>{"use strict";r.r(t),r.d(t,{FunctionsClient:()=>s});class s{constructor(e,{headers:t={},customFetch:s}={}){this.url=e,this.headers=t,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>{return t=void 0,s=void 0,i=function*(){return yield(yield r.e(98).then(r.t.bind(r,98,23))).fetch(...e)},new((n=void 0)||(n=Promise))((function(e,r){function o(e){try{h(i.next(e))}catch(e){r(e)}}function a(e){try{h(i.throw(e))}catch(e){r(e)}}function h(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,a)}h((i=i.apply(t,s||[])).next())}));var t,s,n,i}:fetch),(...e)=>t(...e)})(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t){return r=this,s=void 0,i=function*(){try{const{headers:r,body:s}=null!=t?t:{},n=yield this.fetch(`${this.url}/${e}`,{method:"POST",headers:Object.assign({},this.headers,r),body:s}),i=n.headers.get("x-relay-error");if(i&&"true"===i)return{data:null,error:new Error(yield n.text())};let o;const{responseType:a}=null!=t?t:{};return o=a&&"json"!==a?"arrayBuffer"===a?yield n.arrayBuffer():"blob"===a?yield n.blob():yield n.text():yield n.json(),{data:o,error:null}}catch(e){return{data:null,error:e}}},new((n=void 0)||(n=Promise))((function(e,t){function o(e){try{h(i.next(e))}catch(e){t(e)}}function a(e){try{h(i.throw(e))}catch(e){t(e)}}function h(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,a)}h((i=i.apply(r,s||[])).next())}));var r,s,n,i}}},271:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GoTrueApi:()=>_,GoTrueClient:()=>T});const s="Request Failed",n="supabase.auth.token",i={name:"sb",lifetime:28800,domain:"",path:"/",sameSite:"lax"};var o=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};const a=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e);function h(e,t,r,n,i){return o(this,void 0,void 0,(function*(){return new Promise(((o,h)=>{e(r,((e,t,r)=>{const s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e||(s.headers=Object.assign({"Content-Type":"text/plain;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(r)),s})(t,n,i)).then((e=>{if(!e.ok)throw e;return(null==n?void 0:n.noResolveJson)?o:e.json()})).then((e=>o(e))).catch((e=>((e,t)=>(null==e?void 0:e.status)?"function"!=typeof e.json?t(e):void e.json().then((r=>t({message:a(r),status:(null==e?void 0:e.status)||500}))):t({message:s}))(e,h)))}))}))}function c(e,t,r){return o(this,void 0,void 0,(function*(){return h(e,"GET",t,r)}))}function u(e,t,r,s){return o(this,void 0,void 0,(function*(){return h(e,"POST",t,s,r)}))}function l(e,t,r,s){return o(this,void 0,void 0,(function*(){return h(e,"PUT",t,s,r)}))}function d(e,t,r){const s=r.map((t=>{return r=t,s=function(e){if(!e||!e.headers||!e.headers.host)throw new Error('The "host" request header is not available');const t=e.headers.host.indexOf(":")>-1&&e.headers.host.split(":")[0]||e.headers.host;return!(["localhost","127.0.0.1"].indexOf(t)>-1||t.endsWith(".local"))}(e),function(e,t,r){const s=r||{},n=encodeURIComponent,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;if("function"!=typeof n)throw new TypeError("option encode is invalid");if(!i.test(e))throw new TypeError("argument name is invalid");const o=n(t);if(o&&!i.test(o))throw new TypeError("argument val is invalid");let a=e+"="+o;if(null!=s.maxAge){const e=s.maxAge-0;if(isNaN(e)||!isFinite(e))throw new TypeError("option maxAge is invalid");a+="; Max-Age="+Math.floor(e)}if(s.domain){if(!i.test(s.domain))throw new TypeError("option domain is invalid");a+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw new TypeError("option path is invalid");a+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw new TypeError("option expires is invalid");a+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(a+="; HttpOnly"),s.secure&&(a+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return a}(r.name,r.value,{maxAge:r.maxAge,expires:new Date(Date.now()+1e3*r.maxAge),httpOnly:!0,secure:s,path:null!==(n=r.path)&&void 0!==n?n:"/",domain:null!==(i=r.domain)&&void 0!==i?i:"",sameSite:null!==(o=r.sameSite)&&void 0!==o?o:"lax"});var r,s,n,i,o})),n=t.getHeader("Set-Cookie");return n&&(n instanceof Array?Array.prototype.push.apply(s,n):"string"==typeof n&&s.push(n)),s}function p(e,t,r){t.setHeader("Set-Cookie",d(e,t,r))}var f=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};function v(e){return Math.round(Date.now()/1e3)+e}const m=()=>"undefined"!=typeof window;function g(e,t){var r;t||(t=(null===(r=null===window||void 0===window?void 0:window.location)||void 0===r?void 0:r.href)||""),e=e.replace(/[\[\]]/g,"\\$&");const s=new RegExp("[?&#]"+e+"(=([^&#]*)|&|#|$)").exec(t);return s?s[2]?decodeURIComponent(s[2].replace(/\+/g," ")):"":null}const y=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>f(void 0,void 0,void 0,(function*(){return yield(yield r.e(98).then(r.t.bind(r,98,23))).fetch(...e)})):fetch),(...e)=>t(...e)};var b=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};class _{constructor({url:e="",headers:t={},cookieOptions:r,fetch:s}){this.url=e,this.headers=t,this.cookieOptions=Object.assign(Object.assign({},i),r),this.fetch=y(s)}_createRequestHeaders(e){const t=Object.assign({},this.headers);return t.Authorization=`Bearer ${e}`,t}cookieName(){var e;return null!==(e=this.cookieOptions.name)&&void 0!==e?e:""}getUrlForProvider(e,t){const r=[`provider=${encodeURIComponent(e)}`];if((null==t?void 0:t.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(t.redirectTo)}`),(null==t?void 0:t.scopes)&&r.push(`scopes=${encodeURIComponent(t.scopes)}`),null==t?void 0:t.queryParams){const e=new URLSearchParams(t.queryParams);r.push(`${e}`)}return`${this.url}/authorize?${r.join("&")}`}signUpWithEmail(e,t,r={}){return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers);let n="";r.redirectTo&&(n="?redirect_to="+encodeURIComponent(r.redirectTo));const i=yield u(this.fetch,`${this.url}/signup${n}`,{email:e,password:t,data:r.data,gotrue_meta_security:{captcha_token:r.captchaToken}},{headers:s}),o=Object.assign({},i);return o.expires_in&&(o.expires_at=v(i.expires_in)),{data:o,error:null}}catch(e){return{data:null,error:e}}}))}signInWithEmail(e,t,r={}){return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers);let n="?grant_type=password";r.redirectTo&&(n+="&redirect_to="+encodeURIComponent(r.redirectTo));const i=yield u(this.fetch,`${this.url}/token${n}`,{email:e,password:t,gotrue_meta_security:{captcha_token:r.captchaToken}},{headers:s}),o=Object.assign({},i);return o.expires_in&&(o.expires_at=v(i.expires_in)),{data:o,error:null}}catch(e){return{data:null,error:e}}}))}signUpWithPhone(e,t,r={}){return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers),n=yield u(this.fetch,`${this.url}/signup`,{phone:e,password:t,data:r.data,gotrue_meta_security:{captcha_token:r.captchaToken}},{headers:s}),i=Object.assign({},n);return i.expires_in&&(i.expires_at=v(n.expires_in)),{data:i,error:null}}catch(e){return{data:null,error:e}}}))}signInWithPhone(e,t,r={}){return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers),n="?grant_type=password",i=yield u(this.fetch,`${this.url}/token${n}`,{phone:e,password:t,gotrue_meta_security:{captcha_token:r.captchaToken}},{headers:s}),o=Object.assign({},i);return o.expires_in&&(o.expires_at=v(i.expires_in)),{data:o,error:null}}catch(e){return{data:null,error:e}}}))}signInWithOpenIDConnect({id_token:e,nonce:t,client_id:r,issuer:s,provider:n}){return b(this,void 0,void 0,(function*(){try{const i=Object.assign({},this.headers),o="?grant_type=id_token",a=yield u(this.fetch,`${this.url}/token${o}`,{id_token:e,nonce:t,client_id:r,issuer:s,provider:n},{headers:i}),h=Object.assign({},a);return h.expires_in&&(h.expires_at=v(a.expires_in)),{data:h,error:null}}catch(e){return{data:null,error:e}}}))}sendMagicLinkEmail(e,t={}){var r;return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers);let n="";t.redirectTo&&(n+="?redirect_to="+encodeURIComponent(t.redirectTo));const i=null===(r=t.shouldCreateUser)||void 0===r||r;return{data:yield u(this.fetch,`${this.url}/otp${n}`,{email:e,create_user:i,gotrue_meta_security:{captcha_token:t.captchaToken}},{headers:s}),error:null}}catch(e){return{data:null,error:e}}}))}sendMobileOTP(e,t={}){var r;return b(this,void 0,void 0,(function*(){try{const s=null===(r=t.shouldCreateUser)||void 0===r||r,n=Object.assign({},this.headers);return{data:yield u(this.fetch,`${this.url}/otp`,{phone:e,create_user:s,gotrue_meta_security:{captcha_token:t.captchaToken}},{headers:n}),error:null}}catch(e){return{data:null,error:e}}}))}signOut(e){return b(this,void 0,void 0,(function*(){try{return yield u(this.fetch,`${this.url}/logout`,{},{headers:this._createRequestHeaders(e),noResolveJson:!0}),{error:null}}catch(e){return{error:e}}}))}verifyMobileOTP(e,t,r={}){return b(this,void 0,void 0,(function*(){try{const s=Object.assign({},this.headers),n=yield u(this.fetch,`${this.url}/verify`,{phone:e,token:t,type:"sms",redirect_to:r.redirectTo},{headers:s}),i=Object.assign({},n);return i.expires_in&&(i.expires_at=v(n.expires_in)),{data:i,error:null}}catch(e){return{data:null,error:e}}}))}verifyOTP({email:e,phone:t,token:r,type:s="sms"},n={}){return b(this,void 0,void 0,(function*(){try{const i=Object.assign({},this.headers),o=yield u(this.fetch,`${this.url}/verify`,{email:e,phone:t,token:r,type:s,redirect_to:n.redirectTo},{headers:i}),a=Object.assign({},o);return a.expires_in&&(a.expires_at=v(o.expires_in)),{data:a,error:null}}catch(e){return{data:null,error:e}}}))}inviteUserByEmail(e,t={}){return b(this,void 0,void 0,(function*(){try{const r=Object.assign({},this.headers);let s="";return t.redirectTo&&(s+="?redirect_to="+encodeURIComponent(t.redirectTo)),{data:yield u(this.fetch,`${this.url}/invite${s}`,{email:e,data:t.data},{headers:r}),error:null}}catch(e){return{data:null,error:e}}}))}resetPasswordForEmail(e,t={}){return b(this,void 0,void 0,(function*(){try{const r=Object.assign({},this.headers);let s="";return t.redirectTo&&(s+="?redirect_to="+encodeURIComponent(t.redirectTo)),{data:yield u(this.fetch,`${this.url}/recover${s}`,{email:e,gotrue_meta_security:{captcha_token:t.captchaToken}},{headers:r}),error:null}}catch(e){return{data:null,error:e}}}))}refreshAccessToken(e){return b(this,void 0,void 0,(function*(){try{const t=yield u(this.fetch,`${this.url}/token?grant_type=refresh_token`,{refresh_token:e},{headers:this.headers}),r=Object.assign({},t);return r.expires_in&&(r.expires_at=v(t.expires_in)),{data:r,error:null}}catch(e){return{data:null,error:e}}}))}setAuthCookie(e,t){"POST"!==e.method&&(t.setHeader("Allow","POST"),t.status(405).end("Method Not Allowed"));const{event:r,session:s}=e.body;if(!r)throw new Error("Auth event missing!");if("SIGNED_IN"===r){if(!s)throw new Error("Auth session missing!");p(e,t,[{key:"access-token",value:s.access_token},{key:"refresh-token",value:s.refresh_token}].map((e=>{var t;return{name:`${this.cookieName()}-${e.key}`,value:e.value,domain:this.cookieOptions.domain,maxAge:null!==(t=this.cookieOptions.lifetime)&&void 0!==t?t:0,path:this.cookieOptions.path,sameSite:this.cookieOptions.sameSite}})))}"SIGNED_OUT"===r&&p(e,t,["access-token","refresh-token"].map((e=>({name:`${this.cookieName()}-${e}`,value:"",maxAge:-1})))),t.status(200).json({})}deleteAuthCookie(e,t,{redirectTo:r="/"}){return p(e,t,["access-token","refresh-token"].map((e=>({name:`${this.cookieName()}-${e}`,value:"",maxAge:-1})))),t.redirect(307,r)}getAuthCookieString(e,t){"POST"!==e.method&&(t.setHeader("Allow","POST"),t.status(405).end("Method Not Allowed"));const{event:r,session:s}=e.body;if(!r)throw new Error("Auth event missing!");if("SIGNED_IN"===r){if(!s)throw new Error("Auth session missing!");return d(e,t,[{key:"access-token",value:s.access_token},{key:"refresh-token",value:s.refresh_token}].map((e=>{var t;return{name:`${this.cookieName()}-${e.key}`,value:e.value,domain:this.cookieOptions.domain,maxAge:null!==(t=this.cookieOptions.lifetime)&&void 0!==t?t:0,path:this.cookieOptions.path,sameSite:this.cookieOptions.sameSite}})))}return"SIGNED_OUT"===r?d(e,t,["access-token","refresh-token"].map((e=>({name:`${this.cookieName()}-${e}`,value:"",maxAge:-1})))):t.getHeader("Set-Cookie")}generateLink(e,t,r={}){return b(this,void 0,void 0,(function*(){try{return{data:yield u(this.fetch,`${this.url}/admin/generate_link`,{type:e,email:t,password:r.password,data:r.data,redirect_to:r.redirectTo},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}createUser(e){return b(this,void 0,void 0,(function*(){try{const t=yield u(this.fetch,`${this.url}/admin/users`,e,{headers:this.headers});return{user:t,data:t,error:null}}catch(e){return{user:null,data:null,error:e}}}))}listUsers(){return b(this,void 0,void 0,(function*(){try{return{data:(yield c(this.fetch,`${this.url}/admin/users`,{headers:this.headers})).users,error:null}}catch(e){return{data:null,error:e}}}))}getUserById(e){return b(this,void 0,void 0,(function*(){try{return{data:yield c(this.fetch,`${this.url}/admin/users/${e}`,{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}getUserByCookie(e,t){return b(this,void 0,void 0,(function*(){try{if(!e.cookies)throw new Error("Not able to parse cookies! When using Express make sure the cookie-parser middleware is in use!");const r=e.cookies[`${this.cookieName()}-access-token`],s=e.cookies[`${this.cookieName()}-refresh-token`];if(!r)throw new Error("No cookie found!");const{user:n,error:i}=yield this.getUser(r);if(i){if(!s)throw new Error("No refresh_token cookie found!");if(!t)throw new Error("You need to pass the res object to automatically refresh the session!");const{data:r,error:n}=yield this.refreshAccessToken(s);if(n)throw n;if(r)return p(e,t,[{key:"access-token",value:r.access_token},{key:"refresh-token",value:r.refresh_token}].map((e=>{var t;return{name:`${this.cookieName()}-${e.key}`,value:e.value,domain:this.cookieOptions.domain,maxAge:null!==(t=this.cookieOptions.lifetime)&&void 0!==t?t:0,path:this.cookieOptions.path,sameSite:this.cookieOptions.sameSite}}))),{token:r.access_token,user:r.user,data:r.user,error:null}}return{token:r,user:n,data:n,error:null}}catch(e){return{token:null,user:null,data:null,error:e}}}))}updateUserById(e,t){return b(this,void 0,void 0,(function*(){try{const r=yield l(this.fetch,`${this.url}/admin/users/${e}`,t,{headers:this.headers});return{user:r,data:r,error:null}}catch(e){return{user:null,data:null,error:e}}}))}deleteUser(e){return b(this,void 0,void 0,(function*(){try{const t=yield function(e,t,r,s){return o(this,void 0,void 0,(function*(){return h(e,"DELETE",t,s,r)}))}(this.fetch,`${this.url}/admin/users/${e}`,{},{headers:this.headers});return{user:t,data:t,error:null}}catch(e){return{user:null,data:null,error:e}}}))}getUser(e){return b(this,void 0,void 0,(function*(){try{const t=yield c(this.fetch,`${this.url}/user`,{headers:this._createRequestHeaders(e)});return{user:t,data:t,error:null}}catch(e){return{user:null,data:null,error:e}}}))}updateUser(e,t){return b(this,void 0,void 0,(function*(){try{const r=yield l(this.fetch,`${this.url}/user`,t,{headers:this._createRequestHeaders(e)});return{user:r,data:r,error:null}}catch(e){return{user:null,data:null,error:e}}}))}}var w=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const k={url:"http://localhost:9999",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,multiTab:!0,headers:{"X-Client-Info":"gotrue-js/1.22.22"}};class T{constructor(e){this.stateChangeEmitters=new Map,this.networkRetries=0;const t=Object.assign(Object.assign({},k),e);this.currentUser=null,this.currentSession=null,this.autoRefreshToken=t.autoRefreshToken,this.persistSession=t.persistSession,this.multiTab=t.multiTab,this.localStorage=t.localStorage||globalThis.localStorage,this.api=new _({url:t.url,headers:t.headers,cookieOptions:t.cookieOptions,fetch:t.fetch}),this._recoverSession(),this._recoverAndRefresh(),this._listenForMultiTabEvents(),this._handleVisibilityChange(),t.detectSessionInUrl&&m()&&g("access_token")&&this.getSessionFromUrl({storeSession:!0}).then((({error:e})=>{if(e)throw new Error("Error getting session from URL.")}))}signUp({email:e,password:t,phone:r},s={}){return w(this,void 0,void 0,(function*(){try{this._removeSession();const{data:n,error:i}=r&&t?yield this.api.signUpWithPhone(r,t,{data:s.data,captchaToken:s.captchaToken}):yield this.api.signUpWithEmail(e,t,{redirectTo:s.redirectTo,data:s.data,captchaToken:s.captchaToken});if(i)throw i;if(!n)throw"An error occurred on sign up.";let o=null,a=null;return n.access_token&&(o=n,a=o.user,this._saveSession(o),this._notifyAllSubscribers("SIGNED_IN")),n.id&&(a=n),{user:a,session:o,error:null}}catch(e){return{user:null,session:null,error:e}}}))}signIn({email:e,phone:t,password:r,refreshToken:s,provider:n,oidc:i},o={}){return w(this,void 0,void 0,(function*(){try{if(this._removeSession(),e&&!r){const{error:t}=yield this.api.sendMagicLinkEmail(e,{redirectTo:o.redirectTo,shouldCreateUser:o.shouldCreateUser,captchaToken:o.captchaToken});return{user:null,session:null,error:t}}if(e&&r)return this._handleEmailSignIn(e,r,{redirectTo:o.redirectTo,captchaToken:o.captchaToken});if(t&&!r){const{error:e}=yield this.api.sendMobileOTP(t,{shouldCreateUser:o.shouldCreateUser,captchaToken:o.captchaToken});return{user:null,session:null,error:e}}if(t&&r)return this._handlePhoneSignIn(t,r);if(s){const{error:e}=yield this._callRefreshToken(s);if(e)throw e;return{user:this.currentUser,session:this.currentSession,error:null}}if(n)return this._handleProviderSignIn(n,{redirectTo:o.redirectTo,scopes:o.scopes,queryParams:o.queryParams});if(i)return this._handleOpenIDConnectSignIn(i);throw new Error("You must provide either an email, phone number, a third-party provider or OpenID Connect.")}catch(e){return{user:null,session:null,error:e}}}))}verifyOTP(e,t={}){return w(this,void 0,void 0,(function*(){try{this._removeSession();const{data:r,error:s}=yield this.api.verifyOTP(e,t);if(s)throw s;if(!r)throw"An error occurred on token verification.";let n=null,i=null;return r.access_token&&(n=r,i=n.user,this._saveSession(n),this._notifyAllSubscribers("SIGNED_IN")),r.id&&(i=r),{user:i,session:n,error:null}}catch(e){return{user:null,session:null,error:e}}}))}user(){return this.currentUser}session(){return this.currentSession}refreshSession(){var e;return w(this,void 0,void 0,(function*(){try{if(!(null===(e=this.currentSession)||void 0===e?void 0:e.access_token))throw new Error("Not logged in.");const{error:t}=yield this._callRefreshToken();if(t)throw t;return{data:this.currentSession,user:this.currentUser,error:null}}catch(e){return{data:null,user:null,error:e}}}))}update(e){var t;return w(this,void 0,void 0,(function*(){try{if(!(null===(t=this.currentSession)||void 0===t?void 0:t.access_token))throw new Error("Not logged in.");const{user:r,error:s}=yield this.api.updateUser(this.currentSession.access_token,e);if(s)throw s;if(!r)throw Error("Invalid user data.");const n=Object.assign(Object.assign({},this.currentSession),{user:r});return this._saveSession(n),this._notifyAllSubscribers("USER_UPDATED"),{data:r,user:r,error:null}}catch(e){return{data:null,user:null,error:e}}}))}setSession(e){return w(this,void 0,void 0,(function*(){try{if(!e)throw new Error("No current session.");const{data:t,error:r}=yield this.api.refreshAccessToken(e);return r?{session:null,error:r}:(this._saveSession(t),this._notifyAllSubscribers("SIGNED_IN"),{session:t,error:null})}catch(e){return{error:e,session:null}}}))}setAuth(e){return this.currentSession=Object.assign(Object.assign({},this.currentSession),{access_token:e,token_type:"bearer",user:this.user()}),this._notifyAllSubscribers("TOKEN_REFRESHED"),this.currentSession}getSessionFromUrl(e){return w(this,void 0,void 0,(function*(){try{if(!m())throw new Error("No browser detected.");const t=g("error_description");if(t)throw new Error(t);const r=g("provider_token"),s=g("access_token");if(!s)throw new Error("No access_token detected.");const n=g("expires_in");if(!n)throw new Error("No expires_in detected.");const i=g("refresh_token");if(!i)throw new Error("No refresh_token detected.");const o=g("token_type");if(!o)throw new Error("No token_type detected.");const a=Math.round(Date.now()/1e3)+parseInt(n),{user:h,error:c}=yield this.api.getUser(s);if(c)throw c;const u={provider_token:r,access_token:s,expires_in:parseInt(n),expires_at:a,refresh_token:i,token_type:o,user:h};if(null==e?void 0:e.storeSession){this._saveSession(u);const e=g("type");this._notifyAllSubscribers("SIGNED_IN"),"recovery"===e&&this._notifyAllSubscribers("PASSWORD_RECOVERY")}return window.location.hash="",{data:u,error:null}}catch(e){return{data:null,error:e}}}))}signOut(){var e;return w(this,void 0,void 0,(function*(){const t=null===(e=this.currentSession)||void 0===e?void 0:e.access_token;if(this._removeSession(),this._notifyAllSubscribers("SIGNED_OUT"),t){const{error:e}=yield this.api.signOut(t);if(e)return{error:e}}return{error:null}}))}onAuthStateChange(e){try{const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),r={id:t,callback:e,unsubscribe:()=>{this.stateChangeEmitters.delete(t)}};return this.stateChangeEmitters.set(t,r),{data:r,error:null}}catch(e){return{data:null,error:e}}}_handleEmailSignIn(e,t,r={}){var s,n;return w(this,void 0,void 0,(function*(){try{const{data:i,error:o}=yield this.api.signInWithEmail(e,t,{redirectTo:r.redirectTo,captchaToken:r.captchaToken});return o||!i?{data:null,user:null,session:null,error:o}:(((null===(s=null==i?void 0:i.user)||void 0===s?void 0:s.confirmed_at)||(null===(n=null==i?void 0:i.user)||void 0===n?void 0:n.email_confirmed_at))&&(this._saveSession(i),this._notifyAllSubscribers("SIGNED_IN")),{data:i,user:i.user,session:i,error:null})}catch(e){return{data:null,user:null,session:null,error:e}}}))}_handlePhoneSignIn(e,t,r={}){var s;return w(this,void 0,void 0,(function*(){try{const{data:n,error:i}=yield this.api.signInWithPhone(e,t,r);return i||!n?{data:null,user:null,session:null,error:i}:((null===(s=null==n?void 0:n.user)||void 0===s?void 0:s.phone_confirmed_at)&&(this._saveSession(n),this._notifyAllSubscribers("SIGNED_IN")),{data:n,user:n.user,session:n,error:null})}catch(e){return{data:null,user:null,session:null,error:e}}}))}_handleProviderSignIn(e,t={}){const r=this.api.getUrlForProvider(e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});try{return m()&&(window.location.href=r),{provider:e,url:r,data:null,session:null,user:null,error:null}}catch(t){return r?{provider:e,url:r,data:null,session:null,user:null,error:null}:{data:null,user:null,session:null,error:t}}}_handleOpenIDConnectSignIn({id_token:e,nonce:t,client_id:r,issuer:s,provider:n}){return w(this,void 0,void 0,(function*(){if(e&&t&&(r&&s||n))try{const{data:i,error:o}=yield this.api.signInWithOpenIDConnect({id_token:e,nonce:t,client_id:r,issuer:s,provider:n});return o||!i?{user:null,session:null,error:o}:(this._saveSession(i),this._notifyAllSubscribers("SIGNED_IN"),{user:i.user,session:i,error:null})}catch(e){return{user:null,session:null,error:e}}throw new Error("You must provide a OpenID Connect provider with your id token and nonce.")}))}_recoverSession(){try{const e=((e,t)=>{const r=m()&&(null==e?void 0:e.getItem("supabase.auth.token"));if(!r||"string"!=typeof r)return null;try{return JSON.parse(r)}catch(e){return r}})(this.localStorage);if(!e)return null;const{currentSession:t,expiresAt:r}=e;r>=Math.round(Date.now()/1e3)+10&&(null==t?void 0:t.user)&&(this._saveSession(t),this._notifyAllSubscribers("SIGNED_IN"))}catch(e){console.log("error",e)}}_recoverAndRefresh(){return w(this,void 0,void 0,(function*(){try{const t=yield(e=this.localStorage,n,f(void 0,void 0,void 0,(function*(){const t=m()&&(yield null==e?void 0:e.getItem("supabase.auth.token"));if(!t)return null;try{return JSON.parse(t)}catch(e){return t}})));if(!t)return null;const{currentSession:r,expiresAt:i}=t;if(i<Math.round(Date.now()/1e3)+10)if(this.autoRefreshToken&&r.refresh_token){this.networkRetries++;const{error:e}=yield this._callRefreshToken(r.refresh_token);if(e){if(console.log(e.message),e.message===s&&this.networkRetries<10)return this.refreshTokenTimer&&clearTimeout(this.refreshTokenTimer),void(this.refreshTokenTimer=setTimeout((()=>this._recoverAndRefresh()),100*Math.pow(2,this.networkRetries)));yield this._removeSession()}this.networkRetries=0}else this._removeSession();else r?(this._saveSession(r),this._notifyAllSubscribers("SIGNED_IN")):(console.log("Current session is missing data."),this._removeSession())}catch(e){return console.error(e),null}var e}))}_callRefreshToken(e){var t;return void 0===e&&(e=null===(t=this.currentSession)||void 0===t?void 0:t.refresh_token),w(this,void 0,void 0,(function*(){try{if(!e)throw new Error("No current session.");const{data:t,error:r}=yield this.api.refreshAccessToken(e);if(r)throw r;if(!t)throw Error("Invalid session data.");return this._saveSession(t),this._notifyAllSubscribers("TOKEN_REFRESHED"),this._notifyAllSubscribers("SIGNED_IN"),{data:t,error:null}}catch(e){return{data:null,error:e}}}))}_notifyAllSubscribers(e){this.stateChangeEmitters.forEach((t=>t.callback(e,this.currentSession)))}_saveSession(e){this.currentSession=e,this.currentUser=e.user;const t=e.expires_at;if(t){const e=t-Math.round(Date.now()/1e3),r=e>10?10:.5;this._startAutoRefreshToken(1e3*(e-r))}this.persistSession&&e.expires_at&&this._persistSession(this.currentSession)}_persistSession(e){const t={currentSession:e,expiresAt:e.expires_at};((e,t,r)=>{f(void 0,void 0,void 0,(function*(){m()&&(yield null==e?void 0:e.setItem("supabase.auth.token",JSON.stringify(r)))}))})(this.localStorage,0,t)}_removeSession(){return w(this,void 0,void 0,(function*(){var e;this.currentSession=null,this.currentUser=null,this.refreshTokenTimer&&clearTimeout(this.refreshTokenTimer),e=this.localStorage,f(void 0,void 0,void 0,(function*(){m()&&(yield null==e?void 0:e.removeItem("supabase.auth.token"))}))}))}_startAutoRefreshToken(e){this.refreshTokenTimer&&clearTimeout(this.refreshTokenTimer),e<=0||!this.autoRefreshToken||(this.refreshTokenTimer=setTimeout((()=>w(this,void 0,void 0,(function*(){this.networkRetries++;const{error:e}=yield this._callRefreshToken();e||(this.networkRetries=0),(null==e?void 0:e.message)===s&&this.networkRetries<10&&this._startAutoRefreshToken(100*Math.pow(2,this.networkRetries))}))),e),"function"==typeof this.refreshTokenTimer.unref&&this.refreshTokenTimer.unref())}_listenForMultiTabEvents(){if(!this.multiTab||!m()||!(null===window||void 0===window?void 0:window.addEventListener))return!1;try{null===window||void 0===window||window.addEventListener("storage",(e=>{var t;if(e.key===n){const r=JSON.parse(String(e.newValue));(null===(t=null==r?void 0:r.currentSession)||void 0===t?void 0:t.access_token)?(this._saveSession(r.currentSession),this._notifyAllSubscribers("SIGNED_IN")):(this._removeSession(),this._notifyAllSubscribers("SIGNED_OUT"))}}))}catch(e){console.error("_listenForMultiTabEvents",e)}}_handleVisibilityChange(){if(!this.multiTab||!m()||!(null===window||void 0===window?void 0:window.addEventListener))return!1;try{null===window||void 0===window||window.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&this._recoverAndRefresh()}))}catch(e){console.error("_handleVisibilityChange",e)}}}},501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{PostgrestBuilder:()=>n,PostgrestClient:()=>u,PostgrestFilterBuilder:()=>o,PostgrestQueryBuilder:()=>a});var s=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};class n{constructor(e){let t;Object.assign(this,e),t=e.fetch?e.fetch:"undefined"==typeof fetch?(...e)=>s(this,void 0,void 0,(function*(){return yield(yield r.e(98).then(r.t.bind(r,98,23))).fetch(...e)})):fetch,this.fetch=(...e)=>t(...e),this.shouldThrowOnError=e.shouldThrowOnError||!1,this.allowEmpty=e.allowEmpty||!1}throwOnError(e){return null==e&&(e=!0),this.shouldThrowOnError=e,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=this.fetch(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((e=>s(this,void 0,void 0,(function*(){var t,r,s,n;let i=null,o=null,a=null,h=e.status,c=e.statusText;if(e.ok){const n=null===(t=this.headers.Prefer)||void 0===t?void 0:t.split(",").includes("return=minimal");if("HEAD"!==this.method&&!n){const t=yield e.text();t&&(o="text/csv"===this.headers.Accept?t:JSON.parse(t))}const i=null===(r=this.headers.Prefer)||void 0===r?void 0:r.match(/count=(exact|planned|estimated)/),h=null===(s=e.headers.get("content-range"))||void 0===s?void 0:s.split("/");i&&h&&h.length>1&&(a=parseInt(h[1]))}else{const t=yield e.text();try{i=JSON.parse(t)}catch(e){i={message:t}}if(i&&this.allowEmpty&&(null===(n=null==i?void 0:i.details)||void 0===n?void 0:n.includes("Results contain 0 rows"))&&(i=null,h=200,c="OK"),i&&this.shouldThrowOnError)throw i}return{error:i,data:o,count:a,status:h,statusText:c,body:o}}))));return this.shouldThrowOnError||(r=r.catch((e=>({error:{message:`FetchError: ${e.message}`,details:"",hint:"",code:e.code||""},data:null,body:null,count:null,status:400,statusText:"Bad Request"})))),r.then(e,t)}}class i extends n{select(e="*"){let t=!1;const r=e.split("").map((e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e))).join("");return this.url.searchParams.set("select",r),this}order(e,{ascending:t=!0,nullsFirst:r=!1,foreignTable:s}={}){const n=void 0===s?"order":`${s}.order`,i=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${i?`${i},`:""}${e}.${t?"asc":"desc"}.${r?"nullsfirst":"nullslast"}`),this}limit(e,{foreignTable:t}={}){const r=void 0===t?"limit":`${t}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:r}={}){const s=void 0===r?"offset":`${r}.offset`,n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.headers.Accept="application/vnd.pgrst.object+json",this.allowEmpty=!0,this}csv(){return this.headers.Accept="text/csv",this}}class o extends i{constructor(){super(...arguments),this.cs=this.contains,this.cd=this.containedBy,this.sl=this.rangeLt,this.sr=this.rangeGt,this.nxl=this.rangeGte,this.nxr=this.rangeLte,this.adj=this.rangeAdjacent,this.ov=this.overlaps}not(e,t,r){return this.url.searchParams.append(`${e}`,`not.${t}.${r}`),this}or(e,{foreignTable:t}={}){const r=void 0===t?"or":`${t}.or`;return this.url.searchParams.append(r,`(${e})`),this}eq(e,t){return this.url.searchParams.append(`${e}`,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(`${e}`,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(`${e}`,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(`${e}`,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(`${e}`,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(`${e}`,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(`${e}`,`like.${t}`),this}ilike(e,t){return this.url.searchParams.append(`${e}`,`ilike.${t}`),this}is(e,t){return this.url.searchParams.append(`${e}`,`is.${t}`),this}in(e,t){const r=t.map((e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`)).join(",");return this.url.searchParams.append(`${e}`,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(`${e}`,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(`${e}`,`cs.{${t.join(",")}}`):this.url.searchParams.append(`${e}`,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(`${e}`,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(`${e}`,`cd.{${t.join(",")}}`):this.url.searchParams.append(`${e}`,`cd.${JSON.stringify(t)}`),this}rangeLt(e,t){return this.url.searchParams.append(`${e}`,`sl.${t}`),this}rangeGt(e,t){return this.url.searchParams.append(`${e}`,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(`${e}`,`nxl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(`${e}`,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(`${e}`,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(`${e}`,`ov.${t}`):this.url.searchParams.append(`${e}`,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s=null}={}){let n="";"plain"===s?n="pl":"phrase"===s?n="ph":"websearch"===s&&(n="w");const i=void 0===r?"":`(${r})`;return this.url.searchParams.append(`${e}`,`${n}fts${i}.${t}`),this}fts(e,t,{config:r}={}){const s=void 0===r?"":`(${r})`;return this.url.searchParams.append(`${e}`,`fts${s}.${t}`),this}plfts(e,t,{config:r}={}){const s=void 0===r?"":`(${r})`;return this.url.searchParams.append(`${e}`,`plfts${s}.${t}`),this}phfts(e,t,{config:r}={}){const s=void 0===r?"":`(${r})`;return this.url.searchParams.append(`${e}`,`phfts${s}.${t}`),this}wfts(e,t,{config:r}={}){const s=void 0===r?"":`(${r})`;return this.url.searchParams.append(`${e}`,`wfts${s}.${t}`),this}filter(e,t,r){return this.url.searchParams.append(`${e}`,`${t}.${r}`),this}match(e){return Object.keys(e).forEach((t=>{this.url.searchParams.append(`${t}`,`eq.${e[t]}`)})),this}}class a extends n{constructor(e,{headers:t={},schema:r,fetch:s,shouldThrowOnError:n}={}){super({fetch:s,shouldThrowOnError:n}),this.url=new URL(e),this.headers=Object.assign({},t),this.schema=r}select(e="*",{head:t=!1,count:r=null}={}){this.method="GET";let s=!1;const n=e.split("").map((e=>/\s/.test(e)&&!s?"":('"'===e&&(s=!s),e))).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),t&&(this.method="HEAD"),new o(this)}insert(e,{upsert:t=!1,onConflict:r,returning:s="representation",count:n=null}={}){this.method="POST";const i=[`return=${s}`];if(t&&i.push("resolution=merge-duplicates"),t&&void 0!==r&&this.url.searchParams.set("on_conflict",r),this.body=e,n&&i.push(`count=${n}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new o(this)}upsert(e,{onConflict:t,returning:r="representation",count:s=null,ignoreDuplicates:n=!1}={}){this.method="POST";const i=[`resolution=${n?"ignore":"merge"}-duplicates`,`return=${r}`];return void 0!==t&&this.url.searchParams.set("on_conflict",t),this.body=e,s&&i.push(`count=${s}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),new o(this)}update(e,{returning:t="representation",count:r=null}={}){this.method="PATCH";const s=[`return=${t}`];return this.body=e,r&&s.push(`count=${r}`),this.headers.Prefer&&s.unshift(this.headers.Prefer),this.headers.Prefer=s.join(","),new o(this)}delete({returning:e="representation",count:t=null}={}){this.method="DELETE";const r=[`return=${e}`];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new o(this)}}class h extends n{constructor(e,{headers:t={},schema:r,fetch:s,shouldThrowOnError:n}={}){super({fetch:s,shouldThrowOnError:n}),this.url=new URL(e),this.headers=Object.assign({},t),this.schema=r}rpc(e,{head:t=!1,count:r=null}={}){return t?(this.method="HEAD",e&&Object.entries(e).forEach((([e,t])=>{this.url.searchParams.append(e,t)}))):(this.method="POST",this.body=e),r&&(void 0!==this.headers.Prefer?this.headers.Prefer+=`,count=${r}`:this.headers.Prefer=`count=${r}`),new o(this)}}const c={"X-Client-Info":"postgrest-js/0.37.4"};class u{constructor(e,{headers:t={},schema:r,fetch:s,throwOnError:n}={}){this.url=e,this.headers=Object.assign(Object.assign({},c),t),this.schema=r,this.fetch=s,this.shouldThrowOnError=n}auth(e){return this.headers.Authorization=`Bearer ${e}`,this}from(e){const t=`${this.url}/${e}`;return new a(t,{headers:this.headers,schema:this.schema,fetch:this.fetch,shouldThrowOnError:this.shouldThrowOnError})}rpc(e,t,{head:r=!1,count:s=null}={}){const n=`${this.url}/rpc/${e}`;return new h(n,{headers:this.headers,schema:this.schema,fetch:this.fetch,shouldThrowOnError:this.shouldThrowOnError}).rpc(t,{head:r,count:s})}}},498:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RealtimeClient:()=>O,RealtimeSubscription:()=>S,Transformers:()=>n});var s,n={};r.r(n),r.d(n,{PostgresTypes:()=>s,convertCell:()=>a,convertChangeData:()=>i,convertColumn:()=>o,toArray:()=>d,toBoolean:()=>c,toJson:()=>l,toNumber:()=>u,toTimestampString:()=>p}),function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(s||(s={}));const i=(e,t,r={})=>{var s;const n=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce(((r,s)=>(r[s]=o(s,e,t,n),r)),{})},o=(e,t,r,s)=>{const n=t.find((t=>t.name===e)),i=null==n?void 0:n.type,o=r[e];return i&&!s.includes(i)?a(i,o):h(o)},a=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return d(t,r)}switch(e){case s.bool:return c(t);case s.float4:case s.float8:case s.int2:case s.int4:case s.int8:case s.numeric:case s.oid:return u(t);case s.json:case s.jsonb:return l(t);case s.timestamp:return p(t);case s.abstime:case s.date:case s.daterange:case s.int4range:case s.int8range:case s.money:case s.reltime:case s.text:case s.time:case s.timestamptz:case s.timetz:case s.tsrange:case s.tstzrange:default:return h(t)}},h=e=>e,c=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},u=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},d=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const n=e.slice(1,r);try{s=JSON.parse("["+n+"]")}catch(e){s=n?n.split(","):[]}return s.map((e=>a(t,e)))}return e},p=e=>"string"==typeof e?e.replace(" ","T"):e;var f=r(840);const v={"X-Client-Info":"realtime-js/1.7.5"};var m,g,y,b,_;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(m||(m={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(g||(g={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(y||(y={})),function(e){e.websocket="websocket"}(b||(b={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(_||(_={}));class w{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}class k{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const o=r.decode(e.slice(i,i+s));i+=s;const a=r.decode(e.slice(i,i+n));return i+=n,{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(i,e.byteLength)))}}}class T{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel.joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,(e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout))}trigger(e,t){this.refEvent&&this.channel.trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel.off(this.refEvent)}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter((t=>t.status===e)).forEach((e=>e.callback(t)))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}class S{constructor(e,t={},r){this.topic=e,this.params=t,this.socket=r,this.bindings=[],this.state=g.closed,this.joinedOnce=!1,this.pushBuffer=[],this.timeout=this.socket.timeout,this.joinPush=new T(this,y.join,this.params,this.timeout),this.rejoinTimer=new w((()=>this.rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=g.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((e=>e.send())),this.pushBuffer=[]})),this.onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=g.closed,this.socket.remove(this)})),this.onError((e=>{this.isLeaving()||this.isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=g.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this.isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=g.errored,this.rejoinTimer.scheduleTimeout())})),this.on(y.reply,((e,t)=>{this.trigger(this.replyEventName(t),e)}))}rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this.rejoin()}subscribe(e=this.timeout){if(this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";return this.joinedOnce=!0,this.rejoin(e),this.joinPush}onClose(e){this.on(y.close,e)}onError(e){this.on(y.error,(t=>e(t)))}on(e,t){this.bindings.push({event:e,callback:t})}off(e){this.bindings=this.bindings.filter((t=>t.event!==e))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new T(this,e,t,r);return this.canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=g.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this.trigger(y.close,"leave",this.joinRef())};this.joinPush.destroy();let r=new T(this,y.leave,{},e);return r.receive("ok",(()=>t())).receive("timeout",(()=>t())),r.send(),this.canPush()||r.trigger("ok",{}),r}onMessage(e,t,r){return t}isMember(e){return this.topic===e}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=g.joining,this.joinPush.resend(e))}trigger(e,t,r){let{close:s,error:n,leave:i,join:o}=y;if(r&&[s,n,i,o].indexOf(e)>=0&&r!==this.joinRef())return;let a=this.onMessage(e,t,r);if(t&&!a)throw"channel onMessage callbacks must return the payload, modified or unmodified";this.bindings.filter((r=>"*"===r.event?e===(null==t?void 0:t.type):r.event===e)).map((e=>e.callback(a,r)))}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===g.closed}isErrored(){return this.state===g.errored}isJoined(){return this.state===g.joined}isJoining(){return this.state===g.joining}isLeaving(){return this.state===g.leaving}}const E=()=>{};class O{constructor(e,t){this.accessToken=null,this.channels=[],this.endPoint="",this.headers=v,this.params={},this.timeout=1e4,this.transport=f.w3cwebsocket,this.heartbeatIntervalMs=3e4,this.longpollerTimeout=2e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=E,this.conn=null,this.sendBuffer=[],this.serializer=new k,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.endPoint=`${e}/${b.websocket}`,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.transport)&&(this.transport=t.transport),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs),(null==t?void 0:t.longpollerTimeout)&&(this.longpollerTimeout=t.longpollerTimeout),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new w((()=>{return e=this,t=void 0,s=function*(){yield this.disconnect(),this.connect()},new((r=void 0)||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}));var e,t,r,s}),this.reconnectAfterMs)}connect(){this.conn||(this.conn=new this.transport(this.endPointURL(),[],null,this.headers),this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this.onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e)))}disconnect(e,t){return new Promise(((r,s)=>{try{this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t||""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset()),r({error:null,data:!0})}catch(e){r({error:e,data:!1})}}))}log(e,t,r){this.logger(e,t,r)}onOpen(e){this.stateChangeCallbacks.open.push(e)}onClose(e){this.stateChangeCallbacks.close.push(e)}onError(e){this.stateChangeCallbacks.error.push(e)}onMessage(e){this.stateChangeCallbacks.message.push(e)}connectionState(){switch(this.conn&&this.conn.readyState){case m.connecting:return _.Connecting;case m.open:return _.Open;case m.closing:return _.Closing;default:return _.Closed}}isConnected(){return this.connectionState()===_.Open}remove(e){this.channels=this.channels.filter((t=>t.joinRef()!==e.joinRef()))}channel(e,t={}){const r=new S(e,t,this);return this.channels.push(r),r}push(e){const{topic:t,event:r,payload:s,ref:n}=e;let i=()=>{this.encode(e,(e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)}))};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?i():this.sendBuffer.push(i)}onConnMessage(e){this.decode(e.data,(e=>{let{topic:t,event:r,payload:s,ref:n}=e;(n&&n===this.pendingHeartbeatRef||r===(null==s?void 0:s.type))&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${n&&"("+n+")"||""}`,s),this.channels.filter((e=>e.isMember(t))).forEach((e=>e.trigger(r,s,n))),this.stateChangeCallbacks.message.forEach((t=>t(e)))}))}endPointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}setAuth(e){this.accessToken=e,this.channels.forEach((t=>{e&&t.updateJoinPayload({user_token:e}),t.joinedOnce&&t.isJoined()&&t.push(y.access_token,{access_token:e})}))}leaveOpenTopic(e){let t=this.channels.find((t=>t.topic===e&&(t.isJoined()||t.isJoining())));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_onConnOpen(){this.log("transport",`connected to ${this.endPointURL()}`),this._flushSendBuffer(),this.reconnectTimer.reset(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this._sendHeartbeat()),this.heartbeatIntervalMs),this.stateChangeCallbacks.open.forEach((e=>e()))}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((t=>t(e)))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((t=>t(e)))}_triggerChanError(){this.channels.forEach((e=>e.trigger(y.error)))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((e=>e())),this.sendBuffer=[])}_sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth(this.accessToken)}}}},552:(e,t,r)=>{"use strict";r.r(t),r.d(t,{StorageClient:()=>m,SupabaseStorageClient:()=>m});const s={"X-Client-Info":"storage-js/1.7.3"};var n=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};const i=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e);function o(e,t,r,s,o,a){return n(this,void 0,void 0,(function*(){return new Promise(((n,h)=>{e(r,((e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))})(t,s,o,a)).then((e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?n(e):e.json()})).then((e=>n(e))).catch((e=>((e,t)=>{if("function"!=typeof e.json)return t(e);e.json().then((r=>t({message:i(r),status:(null==e?void 0:e.status)||500})))})(e,h)))}))}))}function a(e,t,r,s){return n(this,void 0,void 0,(function*(){return o(e,"GET",t,r,s)}))}function h(e,t,r,s,i){return n(this,void 0,void 0,(function*(){return o(e,"POST",t,s,i,r)}))}function c(e,t,r,s,i){return n(this,void 0,void 0,(function*(){return o(e,"DELETE",t,s,i,r)}))}const u=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>{return t=void 0,s=void 0,i=function*(){return yield(yield r.e(98).then(r.t.bind(r,98,23))).fetch(...e)},new((n=void 0)||(n=Promise))((function(e,r){function o(e){try{h(i.next(e))}catch(e){r(e)}}function a(e){try{h(i.throw(e))}catch(e){r(e)}}function h(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,a)}h((i=i.apply(t,s||[])).next())}));var t,s,n,i}:fetch),(...e)=>t(...e)};var l=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))},d=function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};const p={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},f={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class v{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=u(s)}uploadOrUpdate(e,t,r,s){return d(this,void 0,void 0,(function*(){try{let n;const i=Object.assign(Object.assign({},f),s),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(n=r,n.append("cacheControl",i.cacheControl)):(n=r,o["cache-control"]=`max-age=${i.cacheControl}`,o["content-type"]=i.contentType);const a=this._removeEmptyFolders(t),h=this._getFinalPath(a),c=yield this.fetch(`${this.url}/object/${h}`,{method:e,body:n,headers:o});return c.ok?{data:{Key:h},error:null}:{data:null,error:yield c.json()}}catch(e){return{data:null,error:e}}}))}upload(e,t,r){return d(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,r)}))}update(e,t,r){return d(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,r)}))}move(e,t){return d(this,void 0,void 0,(function*(){try{return{data:yield h(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}copy(e,t){return d(this,void 0,void 0,(function*(){try{return{data:yield h(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}createSignedUrl(e,t){return d(this,void 0,void 0,(function*(){try{const r=this._getFinalPath(e);let s=yield h(this.fetch,`${this.url}/object/sign/${r}`,{expiresIn:t},{headers:this.headers});const n=`${this.url}${s.signedURL}`;return s={signedURL:n},{data:s,error:null,signedURL:n}}catch(e){return{data:null,error:e,signedURL:null}}}))}createSignedUrls(e,t){return d(this,void 0,void 0,(function*(){try{return{data:(yield h(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers})).map((e=>Object.assign(Object.assign({},e),{signedURL:e.signedURL?`${this.url}${e.signedURL}`:null}))),error:null}}catch(e){return{data:null,error:e}}}))}download(e){return d(this,void 0,void 0,(function*(){try{const t=this._getFinalPath(e),r=yield a(this.fetch,`${this.url}/object/${t}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(e){return{data:null,error:e}}}))}getPublicUrl(e){try{const t=this._getFinalPath(e),r=`${this.url}/object/public/${t}`;return{data:{publicURL:r},error:null,publicURL:r}}catch(e){return{data:null,error:e,publicURL:null}}}remove(e){return d(this,void 0,void 0,(function*(){try{return{data:yield c(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}list(e,t,r){return d(this,void 0,void 0,(function*(){try{const s=Object.assign(Object.assign(Object.assign({},p),t),{prefix:e||""});return{data:yield h(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(e){return{data:null,error:e}}}))}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}}class m extends class{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},s),t),this.fetch=u(r)}listBuckets(){return l(this,void 0,void 0,(function*(){try{return{data:yield a(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}getBucket(e){return l(this,void 0,void 0,(function*(){try{return{data:yield a(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}createBucket(e,t={public:!1}){return l(this,void 0,void 0,(function*(){try{return{data:(yield h(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public},{headers:this.headers})).name,error:null}}catch(e){return{data:null,error:e}}}))}updateBucket(e,t){return l(this,void 0,void 0,(function*(){try{const r=yield function(e,t,r,s,i){return n(this,void 0,void 0,(function*(){return o(e,"PUT",t,s,undefined,r)}))}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public},{headers:this.headers});return{data:r,error:null}}catch(e){return{data:null,error:e}}}))}emptyBucket(e){return l(this,void 0,void 0,(function*(){try{return{data:yield h(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}deleteBucket(e){return l(this,void 0,void 0,(function*(){try{return{data:yield c(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){return{data:null,error:e}}}))}}{constructor(e,t={},r){super(e,t,r)}from(e){return new v(this.url,this.headers,e,this.fetch)}}},284:e=>{var t=function(){if("object"==typeof self&&self)return self;if("object"==typeof window&&window)return window;throw new Error("Unable to resolve global `this`")};e.exports=function(){if(this)return this;if("object"==typeof globalThis&&globalThis)return globalThis;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch(e){return t()}try{return __global__||t()}finally{delete Object.prototype.__global__}}()},296:function(e,t,r){"use strict";var s=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))((function(n,i){function o(e){try{h(s.next(e))}catch(e){i(e)}}function a(e){try{h(s.throw(e))}catch(e){i(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((s=s.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const n=r(678),i=r(610),o=r(283),a=r(528),h=r(552),c=r(248),u=r(501),l=r(498),d={schema:"public",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,multiTab:!0,headers:n.DEFAULT_HEADERS};t.default=class{constructor(e,t,r){if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const s=(0,i.stripTrailingSlash)(e),o=Object.assign(Object.assign({},d),r);if(this.restUrl=`${s}/rest/v1`,this.realtimeUrl=`${s}/realtime/v1`.replace("http","ws"),this.authUrl=`${s}/auth/v1`,this.storageUrl=`${s}/storage/v1`,s.match(/(supabase\.co)|(supabase\.in)/)){const e=s.split(".");this.functionsUrl=`${e[0]}.functions.${e[1]}.${e[2]}`}else this.functionsUrl=`${s}/functions/v1`;this.schema=o.schema,this.multiTab=o.multiTab,this.fetch=o.fetch,this.headers=Object.assign(Object.assign({},n.DEFAULT_HEADERS),null==r?void 0:r.headers),this.shouldThrowOnError=o.shouldThrowOnError||!1,this.auth=this._initSupabaseAuthClient(o),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers},o.realtime)),this._listenForAuthEvents(),this._listenForMultiTabEvents()}get functions(){return new c.FunctionsClient(this.functionsUrl,{headers:this._getAuthHeaders(),customFetch:this.fetch})}get storage(){return new h.SupabaseStorageClient(this.storageUrl,this._getAuthHeaders(),this.fetch)}from(e){const t=`${this.restUrl}/${e}`;return new a.SupabaseQueryBuilder(t,{headers:this._getAuthHeaders(),schema:this.schema,realtime:this.realtime,table:e,fetch:this.fetch,shouldThrowOnError:this.shouldThrowOnError})}rpc(e,t,{head:r=!1,count:s=null}={}){return this._initPostgRESTClient().rpc(e,t,{head:r,count:s})}removeAllSubscriptions(){return s(this,void 0,void 0,(function*(){const e=this.getSubscriptions().slice(),t=e.map((e=>this.removeSubscription(e)));return(yield Promise.all(t)).map((({error:t},r)=>({data:{subscription:e[r]},error:t})))}))}removeSubscription(e){return s(this,void 0,void 0,(function*(){const{error:t}=yield this._closeSubscription(e),r=this.getSubscriptions(),s=r.filter((e=>e.isJoined())).length;return 0===r.length&&(yield this.realtime.disconnect()),{data:{openSubscriptions:s},error:t}}))}_closeSubscription(e){return s(this,void 0,void 0,(function*(){let t=null;if(!e.isClosed()){const{error:r}=yield this._unsubscribeSubscription(e);t=r}return this.realtime.remove(e),{error:t}}))}_unsubscribeSubscription(e){return new Promise((t=>{e.unsubscribe().receive("ok",(()=>t({error:null}))).receive("error",(e=>t({error:e}))).receive("timeout",(()=>t({error:new Error("timed out")})))}))}getSubscriptions(){return this.realtime.channels}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,localStorage:s,headers:n,fetch:i,cookieOptions:a,multiTab:h}){const c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new o.SupabaseAuthClient({url:this.authUrl,headers:Object.assign(Object.assign({},n),c),autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,localStorage:s,fetch:i,cookieOptions:a,multiTab:h})}_initRealtimeClient(e){return new l.RealtimeClient(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign(Object.assign({},null==e?void 0:e.params),{apikey:this.supabaseKey})}))}_initPostgRESTClient(){return new u.PostgrestClient(this.restUrl,{headers:this._getAuthHeaders(),schema:this.schema,fetch:this.fetch,throwOnError:this.shouldThrowOnError})}_getAuthHeaders(){var e,t;const r=Object.assign({},this.headers),s=null!==(t=null===(e=this.auth.session())||void 0===e?void 0:e.access_token)&&void 0!==t?t:this.supabaseKey;return r.apikey=this.supabaseKey,r.Authorization=r.Authorization||`Bearer ${s}`,r}_listenForMultiTabEvents(){if(!this.multiTab||!(0,i.isBrowser)()||!(null===window||void 0===window?void 0:window.addEventListener))return null;try{return null===window||void 0===window?void 0:window.addEventListener("storage",(e=>{var t,r,s;if(e.key===n.STORAGE_KEY){const n=JSON.parse(String(e.newValue)),i=null!==(r=null===(t=null==n?void 0:n.currentSession)||void 0===t?void 0:t.access_token)&&void 0!==r?r:void 0,o=null===(s=this.auth.session())||void 0===s?void 0:s.access_token;i?!o&&i?this._handleTokenChanged("SIGNED_IN",i,"STORAGE"):o!==i&&this._handleTokenChanged("TOKEN_REFRESHED",i,"STORAGE"):this._handleTokenChanged("SIGNED_OUT",i,"STORAGE")}}))}catch(e){return console.error("_listenForMultiTabEvents",e),null}}_listenForAuthEvents(){let{data:e}=this.auth.onAuthStateChange(((e,t)=>{this._handleTokenChanged(e,null==t?void 0:t.access_token,"CLIENT")}));return e}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===t?"SIGNED_OUT"!==e&&"USER_DELETED"!==e||(this.realtime.setAuth(this.supabaseKey),"STORAGE"==r&&this.auth.signOut()):(this.realtime.setAuth(t),"STORAGE"==r&&this.auth.setAuth(t),this.changedAccessToken=t)}}},341:function(e,t,r){"use strict";var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,n)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.AuthSession=t.AuthUser=t.SupabaseRealtimePayload=t.SupabaseClientOptions=t.SupabaseClient=t.createClient=t.PostgrestError=t.PostgrestMaybeSingleResponse=t.PostgrestSingleResponse=t.PostgrestResponse=void 0;const o=i(r(296));t.SupabaseClient=o.default;const a=r(717);Object.defineProperty(t,"SupabaseClientOptions",{enumerable:!0,get:function(){return a.SupabaseClientOptions}}),Object.defineProperty(t,"SupabaseRealtimePayload",{enumerable:!0,get:function(){return a.SupabaseRealtimePayload}});const h=r(271);Object.defineProperty(t,"AuthUser",{enumerable:!0,get:function(){return h.User}}),Object.defineProperty(t,"AuthSession",{enumerable:!0,get:function(){return h.Session}}),n(r(271),t);var c=r(501);Object.defineProperty(t,"PostgrestResponse",{enumerable:!0,get:function(){return c.PostgrestResponse}}),Object.defineProperty(t,"PostgrestSingleResponse",{enumerable:!0,get:function(){return c.PostgrestSingleResponse}}),Object.defineProperty(t,"PostgrestMaybeSingleResponse",{enumerable:!0,get:function(){return c.PostgrestMaybeSingleResponse}}),Object.defineProperty(t,"PostgrestError",{enumerable:!0,get:function(){return c.PostgrestError}}),n(r(498),t),t.createClient=(e,t,r)=>new o.default(e,t,r)},283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SupabaseAuthClient=void 0;const s=r(271);class n extends s.GoTrueClient{constructor(e){super(e)}}t.SupabaseAuthClient=n},528:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SupabaseQueryBuilder=void 0;const s=r(501),n=r(308);class i extends s.PostgrestQueryBuilder{constructor(e,{headers:t={},schema:r,realtime:s,table:n,fetch:i,shouldThrowOnError:o}){super(e,{headers:t,schema:r,fetch:i,shouldThrowOnError:o}),this._subscription=null,this._realtime=s,this._headers=t,this._schema=r,this._table=n}on(e,t){return this._realtime.isConnected()||this._realtime.connect(),this._subscription||(this._subscription=new n.SupabaseRealtimeClient(this._realtime,this._headers,this._schema,this._table)),this._subscription.on(e,t)}}t.SupabaseQueryBuilder=i},308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SupabaseRealtimeClient=void 0;const s=r(498);t.SupabaseRealtimeClient=class{constructor(e,t,r,s){const n={},i="*"===s?`realtime:${r}`:`realtime:${r}:${s}`,o=t.Authorization.split(" ")[1];o&&(n.user_token=o),this.subscription=e.channel(i,n)}getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=s.Transformers.convertChangeData(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=s.Transformers.convertChangeData(e.columns,e.old_record)),t}on(e,t){return this.subscription.on(e,(e=>{let r={schema:e.schema,table:e.table,commit_timestamp:e.commit_timestamp,eventType:e.type,new:{},old:{},errors:e.errors};r=Object.assign(Object.assign({},r),this.getPayloadRecords(e)),t(r)})),this}subscribe(e=(()=>{})){return this.subscription.onError((t=>e("SUBSCRIPTION_ERROR",t))),this.subscription.onClose((()=>e("CLOSED"))),this.subscription.subscribe().receive("ok",(()=>e("SUBSCRIBED"))).receive("error",(t=>e("SUBSCRIPTION_ERROR",t))).receive("timeout",(()=>e("RETRYING_AFTER_TIMEOUT"))),this.subscription}}},678:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.STORAGE_KEY=t.DEFAULT_HEADERS=void 0;const s=r(506);t.DEFAULT_HEADERS={"X-Client-Info":`supabase-js/${s.version}`},t.STORAGE_KEY="supabase.auth.token"},610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isBrowser=t.stripTrailingSlash=t.uuid=void 0,t.uuid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))},t.stripTrailingSlash=function(e){return e.replace(/\/$/,"")},t.isBrowser=()=>"undefined"!=typeof window},717:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},506:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="1.35.7"},840:(e,t,r)=>{var s;if("object"==typeof globalThis)s=globalThis;else try{s=r(284)}catch(e){}finally{if(s||"undefined"==typeof window||(s=window),!s)throw new Error("Could not determine global this")}var n=s.WebSocket||s.MozWebSocket,i=r(387);function o(e,t){return t?new n(e,t):new n(e)}n&&["CONNECTING","OPEN","CLOSING","CLOSED"].forEach((function(e){Object.defineProperty(o,e,{get:function(){return n[e]}})})),e.exports={w3cwebsocket:n?o:null,version:i}},387:(e,t,r)=>{e.exports=r(794).version},794:e=>{"use strict";e.exports={version:"1.0.34"}}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return n[e].call(r.exports,r,r.exports,o),r.exports}return o.m=n,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(r,s){if(1&s&&(r=this(r)),8&s)return r;if("object"==typeof r&&r){if(4&s&&r.__esModule)return r;if(16&s&&"function"==typeof r.then)return r}var n=Object.create(null);o.r(n);var i={};e=e||[null,t({}),t([]),t(t)];for(var a=2&s&&r;"object"==typeof a&&!~e.indexOf(a);a=t(a))Object.getOwnPropertyNames(a).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,o.d(n,i),n},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((t,r)=>(o.f[r](e,t),t)),[])),o.u=e=>e+".supabase.js",o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},s="supabase:",o.l=(e,t,n,i)=>{if(r[e])r[e].push(t);else{var a,h;if(void 0!==n)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var l=c[u];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==s+n){a=l;break}}a||(h=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,o.nc&&a.setAttribute("nonce",o.nc),a.setAttribute("data-webpack",s+n),a.src=e),r[e]=[t];var d=(t,s)=>{a.onerror=a.onload=null,clearTimeout(p);var n=r[e];if(delete r[e],a.parentNode&&a.parentNode.removeChild(a),n&&n.forEach((e=>e(s))),t)return t(s)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=d.bind(null,a.onerror),a.onload=d.bind(null,a.onload),h&&document.head.appendChild(a)}},o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;o.g.importScripts&&(e=o.g.location+"");var t=o.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");r.length&&(e=r[r.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),o.p=e})(),(()=>{var e={179:0};o.f.j=(t,r)=>{var s=o.o(e,t)?e[t]:void 0;if(0!==s)if(s)r.push(s[2]);else{var n=new Promise(((r,n)=>s=e[t]=[r,n]));r.push(s[2]=n);var i=o.p+o.u(t),a=new Error;o.l(i,(r=>{if(o.o(e,t)&&(0!==(s=e[t])&&(e[t]=void 0),s)){var n=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;a.message="Loading chunk "+t+" failed.\n("+n+": "+i+")",a.name="ChunkLoadError",a.type=n,a.request=i,s[1](a)}}),"chunk-"+t,t)}};var t=(t,r)=>{var s,n,[i,a,h]=r,c=0;if(i.some((t=>0!==e[t]))){for(s in a)o.o(a,s)&&(o.m[s]=a[s]);h&&h(o)}for(t&&t(r);c<i.length;c++)n=i[c],o.o(e,n)&&e[n]&&e[n][0](),e[n]=0},r=self.webpackChunksupabase=self.webpackChunksupabase||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),o(341)})()));