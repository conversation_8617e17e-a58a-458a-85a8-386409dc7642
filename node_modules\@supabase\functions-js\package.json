{"name": "@supabase/functions-js", "version": "1.3.4", "description": "JS Client library to interact with Supabase Functions.", "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist docs", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "docs": "typedoc src/index.ts", "docs:json": "typedoc --json docs/spec.json --excludeExternals src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/functions-js.git"}, "keywords": ["functions", "supabase"], "author": "Supabase", "files": ["dist", "src"], "license": "MIT", "bugs": {"url": "https://github.com/supabase/functions-js/issues"}, "homepage": "https://github.com/supabase/functions-js#readme", "dependencies": {"cross-fetch": "^3.1.5"}, "devDependencies": {"npm-run-all": "^4.1.5", "prettier": "^2.6.0", "rimraf": "^3.0.2", "semantic-release": "^19.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "typedoc": "^0.22.13", "typescript": "^4.6.2"}, "publishConfig": {"access": "public"}}