{"version": 3, "file": "SupabaseQueryBuilder.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/SupabaseQueryBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAA;AAC9D,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAA;AACjE,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,MAAM,SAAS,CAAA;AAE3F,qBAAa,oBAAoB,CAAC,CAAC,CAAE,SAAQ,qBAAqB,CAAC,CAAC,CAAC;IACnE,OAAO,CAAC,aAAa,CAAsC;IAC3D,OAAO,CAAC,SAAS,CAAgB;IACjC,OAAO,CAAC,QAAQ,CAAe;IAC/B,OAAO,CAAC,OAAO,CAAQ;IACvB,OAAO,CAAC,MAAM,CAAQ;gBAGpB,GAAG,EAAE,MAAM,EACX,EACE,OAAY,EACZ,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK,EACL,kBAAkB,GACnB,EAAE;QACD,OAAO,CAAC,EAAE,aAAa,CAAA;QACvB,MAAM,EAAE,MAAM,CAAA;QACd,QAAQ,EAAE,cAAc,CAAA;QACxB,KAAK,EAAE,MAAM,CAAA;QACb,KAAK,CAAC,EAAE,KAAK,CAAA;QACb,kBAAkB,CAAC,EAAE,OAAO,CAAA;KAC7B;IAUH;;;;OAIG;IACH,EAAE,CACA,KAAK,EAAE,kBAAkB,EACzB,QAAQ,EAAE,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAC,CAAC,KAAK,IAAI,GACtD,sBAAsB;CAc1B"}