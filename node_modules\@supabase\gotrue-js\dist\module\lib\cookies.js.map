{"version": 3, "file": "cookies.js", "sourceRoot": "", "sources": ["../../../src/lib/cookies.ts"], "names": [], "mappings": "AASA;;GAEG;AACH,SAAS,SAAS,CAChB,IAAY,EACZ,GAAW,EACX,OAQC;IAED,MAAM,GAAG,GAAG,OAAO,IAAI,EAAE,CAAA;IACzB,MAAM,GAAG,GAAG,kBAAkB,CAAA;IAC9B,+CAA+C;IAC/C,MAAM,kBAAkB,GAAG,uCAAuC,CAAA;IAElE,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAA;KAChD;IAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAA;KAChD;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;IAEtB,IAAI,KAAK,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5C,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAA;KAC/C;IAED,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,CAAA;IAE5B,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;QACtB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA;QAE7B,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACtC,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAA;SAChD;QAED,GAAG,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;KACzC;IAED,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACxC,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAA;SAChD;QAED,GAAG,IAAI,WAAW,GAAG,GAAG,CAAC,MAAM,CAAA;KAChC;IAED,IAAI,GAAG,CAAC,IAAI,EAAE;QACZ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAA;SAC9C;QAED,GAAG,IAAI,SAAS,GAAG,GAAG,CAAC,IAAI,CAAA;KAC5B;IAED,IAAI,GAAG,CAAC,OAAO,EAAE;QACf,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;YACjD,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAA;SACjD;QAED,GAAG,IAAI,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAA;KAChD;IAED,IAAI,GAAG,CAAC,QAAQ,EAAE;QAChB,GAAG,IAAI,YAAY,CAAA;KACpB;IAED,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,GAAG,IAAI,UAAU,CAAA;KAClB;IAED,IAAI,GAAG,CAAC,QAAQ,EAAE;QAChB,MAAM,QAAQ,GAAG,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAA;QAE7F,QAAQ,QAAQ,EAAE;YAChB,KAAK,KAAK;gBACR,GAAG,IAAI,gBAAgB,CAAA;gBACvB,MAAK;YACP,KAAK,QAAQ;gBACX,GAAG,IAAI,mBAAmB,CAAA;gBAC1B,MAAK;YACP,KAAK,MAAM;gBACT,GAAG,IAAI,iBAAiB,CAAA;gBACxB,MAAK;YACP;gBACE,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAA;SACpD;KACF;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,GAAQ;IACnC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;KAC9D;IAED,MAAM,IAAI,GACR,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAA;IAC5F,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC5E,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,MAAc,EAAE,MAAe;;IACtD,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;QAC1C,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QACpD,QAAQ,EAAE,IAAI;QACd,MAAM;QACN,IAAI,EAAE,MAAA,MAAM,CAAC,IAAI,mCAAI,GAAG;QACxB,MAAM,EAAE,MAAA,MAAM,CAAC,MAAM,mCAAI,EAAE;QAC3B,QAAQ,EAAE,MAAA,MAAM,CAAC,QAAQ,mCAAI,KAAK;KACnC,CAAC,CAAA;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,GAAQ,EAAE,GAAQ,EAAE,OAAsB;IACxE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACnF,MAAM,eAAe,GAAG,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;IACnD,IAAI,eAAe,EAAE;QACnB,IAAI,eAAe,YAAY,KAAK,EAAE;YACpC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,eAAe,CAAC,CAAA;SACxD;aAAM,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YAC9C,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;SACjC;KACF;IACD,OAAO,UAAU,CAAA;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,GAAQ,EAAE,GAAQ,EAAE,OAAsB;IACnE,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;AACjE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,GAAQ,EAAE,GAAQ,EAAE,MAAc;IAC1D,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;AAChC,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAY;IAC3D,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE;QAClB,IAAI;QACJ,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,CAAC,CAAC;KACX,CAAC,CAAA;AACJ,CAAC"}