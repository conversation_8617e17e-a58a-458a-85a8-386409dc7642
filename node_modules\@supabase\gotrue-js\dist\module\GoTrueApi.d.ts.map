{"version": 3, "file": "GoTrueApi.d.ts", "sourceRoot": "", "sources": ["../../src/GoTrueApi.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAA0B,MAAM,aAAa,CAAA;AAC3D,OAAO,EACL,OAAO,EACP,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,IAAI,EACJ,wBAAwB,EACxB,eAAe,EAChB,MAAM,aAAa,CAAA;AAKpB,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAA;AAC3C,MAAM,CAAC,OAAO,OAAO,SAAS;IAC5B,SAAS,CAAC,GAAG,EAAE,MAAM,CAAA;IACrB,SAAS,CAAC,OAAO,EAAE;QACjB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KACtB,CAAA;IACD,SAAS,CAAC,aAAa,EAAE,aAAa,CAAA;IACtC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAA;gBAEV,EACV,GAAQ,EACR,OAAY,EACZ,aAAa,EACb,KAAK,GACN,EAAE;QACD,GAAG,EAAE,MAAM,CAAA;QACX,OAAO,CAAC,EAAE;YACR,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SACtB,CAAA;QACD,aAAa,CAAC,EAAE,aAAa,CAAA;QAC7B,KAAK,CAAC,EAAE,KAAK,CAAA;KACd;IAOD;;;;OAIG;IACH,OAAO,CAAC,qBAAqB;IAM7B,OAAO,CAAC,UAAU;IAIlB;;;;;OAKG;IACH,iBAAiB,CACf,QAAQ,EAAE,QAAQ,EAClB,OAAO,EAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,WAAW,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SAAE,CAAA;KACxC;IAgBH;;;;;;;;;;OAUG;IACG,eAAe,CACnB,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IA0BnE;;;;;;OAMG;IACG,eAAe,CACnB,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAqB5D;;;;;;OAMG;IACG,eAAe,CACnB,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAsBnE;;;;;OAKG;IACG,eAAe,CACnB,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAkB5D;;;;;;;OAOG;IACG,uBAAuB,CAAC,EAC5B,QAAQ,EACR,KAAK,EACL,SAAS,EACT,MAAM,EACN,QAAQ,GACT,EAAE,wBAAwB,GAAG,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAkBvF;;;;;;OAMG;IACG,kBAAkB,CACtB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAyBvD;;;;;OAKG;IACG,aAAa,CACjB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAoBvD;;;OAGG;IACG,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAc/D;;;;;OAKG;IACG,eAAe,CACnB,KAAK,EAAE,MAAM,EACb,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;KACf,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAiBnE;;;;;;;OAOG;IACG,SAAS,CACb,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE,eAAe,EACtD,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;KACf,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAiBnE;;;;;OAKG;IACG,iBAAiB,CACrB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,IAAI,CAAC,EAAE,MAAM,CAAA;KACT,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAmBzD;;;;;OAKG;IACG,qBAAqB,CACzB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAmBvD;;;OAGG;IACG,kBAAkB,CACtB,YAAY,EAAE,MAAM,GACnB,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAgB5D;;;;;OAKG;IACH,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAwChC;;;;;OAKG;IACH,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAgB,EAAE,EAAE;QAAE,UAAU,CAAC,EAAE,MAAM,CAAA;KAAE;IAalF;;;;;OAKG;IACH,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,MAAM,EAAE;IAwCjD;;;;;;;OAOG;IACG,YAAY,CAChB,IAAI,EACA,QAAQ,GACR,WAAW,GACX,UAAU,GACV,QAAQ,GACR,sBAAsB,GACtB,kBAAkB,EACtB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,UAAU,CAAC,EAAE,MAAM,CAAA;KACf,GACL,OAAO,CAAC;QAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAsBnE;;;;;;OAMG;IACG,UAAU,CACd,UAAU,EAAE,mBAAmB,GAC9B,OAAO,CACR;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,CAAA;KAAE,GAAG;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,CACtF;IAWD;;;;OAIG;IACG,SAAS,IAAI,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,CAAA;KAAE,GAAG;QAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,CAAC;IAW3F;;;;;;OAMG;IACG,WAAW,CACf,GAAG,EAAE,MAAM,GACV,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,CAAA;KAAE,GAAG;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,CAAC;IAWzE;;;OAGG;IACG,eAAe,CACnB,GAAG,EAAE,GAAG,EACR,GAAG,CAAC,EAAE,GAAG,GACR,OAAO,CAAC;QACT,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;QACpB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,IAAI,EAAE,IAAI,GAAG,IAAI,CAAA;QACjB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KACvB,CAAC;IAgDF;;;;;;OAMG;IACG,cAAc,CAClB,GAAG,EAAE,MAAM,EACX,UAAU,EAAE,mBAAmB,GAC9B,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAY5E;;;;;;OAMG;IACG,UAAU,CACd,GAAG,EAAE,MAAM,GACV,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAgB5E;;;;;;;;OAQG;IACG,OAAO,CACX,GAAG,EAAE,MAAM,GACV,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;IAW5E;;;;OAIG;IACG,UAAU,CACd,GAAG,EAAE,MAAM,EACX,UAAU,EAAE,cAAc,GACzB,OAAO,CAAC;QAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;QAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAE,CAAC;CAU7E"}