{"version": 3, "file": "PostgrestTransformBuilder.js", "sourceRoot": "", "sources": ["../../../src/lib/PostgrestTransformBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAyD,MAAM,SAAS,CAAA;AAEjG;;GAEG;AAEH,MAAM,CAAC,OAAO,OAAO,yBAA6B,SAAQ,gBAAmB;IAC3E;;;;OAIG;IACH,MAAM,CAAC,OAAO,GAAG,GAAG;QAClB,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,OAAO;aAC3B,KAAK,CAAC,EAAE,CAAC;aACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CACH,MAAe,EACf,EACE,SAAS,GAAG,IAAI,EAChB,UAAU,GAAG,KAAK,EAClB,YAAY,MAC4D,EAAE;QAE5E,MAAM,GAAG,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,QAAQ,CAAA;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEpD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CACvB,GAAG,EACH,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,IAChF,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAC9B,EAAE,CACH,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAa,EAAE,EAAE,YAAY,KAAgC,EAAE;QACnE,MAAM,GAAG,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,QAAQ,CAAA;QACnF,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAY,EAAE,EAAU,EAAE,EAAE,YAAY,KAAgC,EAAE;QAC9E,MAAM,SAAS,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,SAAS,CAAA;QAC3F,MAAM,QAAQ,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,QAAQ,CAAA;QACxF,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;QAC/C,+BAA+B;QAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;QACvD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;OAGG;IACH,MAAM;QACJ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;QAC5D,OAAO,IAA+C,CAAA;IACxD,CAAC;IAED;;;;OAIG;IACH,WAAW;QACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,OAAO,IAAoD,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,GAAG;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAA;QACnC,OAAO,IAAoD,CAAA;IAC7D,CAAC;CACF"}