{"version": 3, "file": "RealtimeClient.js", "sourceRoot": "", "sources": ["../../src/RealtimeClient.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,yCAAwC;AACxC,+CASwB;AACxB,wDAA+B;AAC/B,kEAAyC;AACzC,kFAAyD;AA2BzD,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAA;AAErB,MAAqB,cAAc;IAiCjC;;;;;;;;;;;;;;OAcG;IACH,YAAY,QAAgB,EAAE,OAAiB;QA/C/C,gBAAW,GAAkB,IAAI,CAAA;QACjC,aAAQ,GAA2B,EAAE,CAAA;QACrC,aAAQ,GAAW,EAAE,CAAA;QACrB,YAAO,GAA+B,2BAAe,CAAA;QACrD,WAAM,GAA+B,EAAE,CAAA;QACvC,YAAO,GAAW,2BAAe,CAAA;QACjC,cAAS,GAAQ,wBAAY,CAAA;QAC7B,wBAAmB,GAAW,KAAK,CAAA;QACnC,sBAAiB,GAAW,KAAK,CAAA;QACjC,mBAAc,GAA+C,SAAS,CAAA;QACtE,wBAAmB,GAAkB,IAAI,CAAA;QACzC,QAAG,GAAW,CAAC,CAAA;QAEf,WAAM,GAAa,IAAI,CAAA;QAIvB,SAAI,GAAqB,IAAI,CAAA;QAC7B,eAAU,GAAe,EAAE,CAAA;QAC3B,eAAU,GAAe,IAAI,oBAAU,EAAE,CAAA;QACzC,yBAAoB,GAKhB;YACF,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,EAAE;SACZ,CAAA;QAkBC,IAAI,CAAC,QAAQ,GAAG,GAAG,QAAQ,IAAI,sBAAU,CAAC,SAAS,EAAE,CAAA;QAErD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;YAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QACjD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO;YAAE,IAAI,CAAC,OAAO,mCAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAC,OAAO,CAAE,CAAA;QAC5E,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QACpD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;YAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QACjD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;YAAE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QAC1D,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB;YAC9B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAA;QACxD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB;YAC5B,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAA;QAEpD,IAAI,CAAC,gBAAgB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EAC/C,CAAC,CAAC,OAAO,CAAC,gBAAgB;YAC1B,CAAC,CAAC,CAAC,KAAa,EAAE,EAAE;gBAChB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,CAAA;YACtD,CAAC,CAAA;QACL,IAAI,CAAC,MAAM,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAC3B,CAAC,CAAC,OAAO,CAAC,MAAM;YAChB,CAAC,CAAC,CAAC,OAAa,EAAE,QAAkB,EAAE,EAAE;gBACpC,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;YAC1C,CAAC,CAAA;QACL,IAAI,CAAC,MAAM,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAC3B,CAAC,CAAC,OAAO,CAAC,MAAM;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAChD,IAAI,CAAC,cAAc,GAAG,IAAI,eAAK,CAAC,GAAS,EAAE;YACzC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YACvB,IAAI,CAAC,OAAO,EAAE,CAAA;QAChB,CAAC,CAAA,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAM;SACP;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAE1E,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,2DAA2D;YAC3D,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,aAAa,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAA;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAmB,CAAC,CAAA;YACrE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;SACxD;IACH,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACR,IAAa,EACb,MAAe;QAEf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YACtC,IAAI;gBACF,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,cAAa,CAAC,CAAA,CAAC,OAAO;oBAC1C,IAAI,IAAI,EAAE;wBACR,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC,CAAA;qBACpC;yBAAM;wBACL,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA;qBAClB;oBACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;oBAChB,sBAAsB;oBACtB,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;oBACzD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA;iBAC5B;gBACD,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;aACrC;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,EAAE,KAAK,EAAE,KAAc,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;aAChD;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,IAAY,EAAE,GAAW,EAAE,IAAU;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,QAAkB;QACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;;;;;;OAOG;IACH,OAAO,CAAC,QAAkB;QACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChD,CAAC;IAED;;;;;;;OAOG;IACH,OAAO,CAAC,QAAkB;QACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChD,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,CAAC,QAAkB;QAC1B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,eAAe;QACb,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACzC,KAAK,yBAAa,CAAC,UAAU;gBAC3B,OAAO,4BAAgB,CAAC,UAAU,CAAA;YACpC,KAAK,yBAAa,CAAC,IAAI;gBACrB,OAAO,4BAAgB,CAAC,IAAI,CAAA;YAC9B,KAAK,yBAAa,CAAC,OAAO;gBACxB,OAAO,4BAAgB,CAAC,OAAO,CAAA;YACjC;gBACE,OAAO,4BAAgB,CAAC,MAAM,CAAA;SACjC;IACH,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,eAAe,EAAE,KAAK,4BAAgB,CAAC,IAAI,CAAA;IACzD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAA6B;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,CAAuB,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE,CAC/D,CAAA;IACH,CAAC;IAED,OAAO,CAAC,KAAa,EAAE,aAA4B,EAAE;QACnD,MAAM,IAAI,GAAG,IAAI,8BAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC9D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxB,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,IAAa;QAChB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAC3C,IAAI,QAAQ,GAAG,GAAG,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAW,EAAE,EAAE;;gBAChC,MAAA,IAAI,CAAC,IAAI,0CAAE,IAAI,CAAC,MAAM,EAAC;YACzB,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QACD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,QAAQ,EAAE,CAAA;SACX;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC/B;IACH,CAAC;IAED,aAAa,CAAC,UAAyB;QACrC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAY,EAAE,EAAE;YAC5C,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;YAExC,IACE,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,mBAAmB,CAAC;gBACzC,KAAK,MAAK,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAA,EACvB;gBACA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;aAChC;YAED,IAAI,CAAC,GAAG,CACN,SAAS,EACT,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,IAAI,KAAK,IACvC,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,EAC9B,EAAE,EACF,OAAO,CACR,CAAA;YACD,IAAI,CAAC,QAAQ;iBACV,MAAM,CAAC,CAAC,OAA6B,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAClE,OAAO,CAAC,CAAC,OAA6B,EAAE,EAAE,CACzC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CACrC,CAAA;YACH,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,eAAG,EAAE,CAAC,CAC7C,CAAA;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;SACb;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,MAAM,CAAA;SAClB;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,KAAoB;QAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QAExB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAChC,KAAK,IAAI,OAAO,CAAC,iBAAiB,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAA;YAEzD,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;gBAC5C,OAAO,CAAC,IAAI,CAAC,0BAAc,CAAC,YAAY,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAA;aACnE;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAa;QAC1B,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAC5D,CAAA;QACD,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,4BAA4B,KAAK,GAAG,CAAC,CAAA;YAC3D,UAAU,CAAC,WAAW,EAAE,CAAA;SACzB;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,gBAAgB,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QAC3D,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA;QAC3B,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACzD,IAAI,CAAC,cAAc,GAAG,WAAW,CAC/B,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,EAC3B,IAAI,CAAC,mBAAmB,CACzB,CAAA;QACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAE,CAAA;IACnE,CAAC;IAEO,YAAY,CAAC,KAAU;QAC7B,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;QACrC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACzD,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IACxE,CAAC;IAEO,YAAY,CAAC,KAAiB;QACpC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;QACpC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IACxE,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAA6B,EAAE,EAAE,CACtD,OAAO,CAAC,OAAO,CAAC,0BAAc,CAAC,KAAK,CAAC,CACtC,CAAA;IACH,CAAC;IAEO,aAAa,CACnB,GAAW,EACX,MAAiC;QAEjC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,GAAG,CAAA;SACX;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAC1C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAA;QAEzC,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAA;IAClC,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACpD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAA;YACjD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;SACrB;IACH,CAAC;IAEO,cAAc;;QACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAM;SACP;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YAC/B,IAAI,CAAC,GAAG,CACN,WAAW,EACX,0DAA0D,CAC3D,CAAA;YACD,MAAA,IAAI,CAAC,IAAI,0CAAE,KAAK,CAAC,2BAAe,EAAE,kBAAkB,EAAC;YACrD,OAAM;SACP;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE,EAAE;YACX,GAAG,EAAE,IAAI,CAAC,mBAAmB;SAC9B,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAChC,CAAC;CACF;AAnZD,iCAmZC"}