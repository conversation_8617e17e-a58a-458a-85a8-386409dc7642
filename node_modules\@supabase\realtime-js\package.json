{"name": "@supabase/realtime-js", "version": "1.7.5", "description": "Listen to realtime updates to your PostgreSQL database", "keywords": ["realtime", "phoenix", "elixir", "javascript", "typescript", "firebase", "supabase"], "homepage": "https://github.com/supabase/realtime-js", "bugs": "https://github.com/supabase/realtime-js/issues", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "repository": "https://github.com/supabase/realtime-js", "author": "Supabase", "license": "MIT", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist docs", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "mocha": "node -r esm node_modules/.bin/mocha ./test/**/*.js -r jsdom-global/register", "test": "run-s clean build mocha", "v1:docs": "typedoc --mode file --target ES6 --theme minimal --out docs/v1", "v1:docs:json": "typedoc --json docs/v1/spec.json --mode modules --includeDeclarations --excludeExternals", "v2:docs": "typedoc --mode file --target ES6 --theme minimal --out docs/v2", "v2:docs:json": "typedoc --json docs/v2/spec.json --mode modules --includeDeclarations --excludeExternals"}, "dependencies": {"@types/phoenix": "^1.5.4", "websocket": "^1.0.34"}, "devDependencies": {"@babel/runtime": "^7.9.2", "@types/websocket": "^1.0.3", "babel-preset-env": "^1.7.0", "babel-register": "^6.26.0", "eslint": "^7.0.0", "esm": "^3.2.25", "jsdom": "16.5.0", "jsdom-global": "3.0.0", "mocha": "^8.0.1", "mock-socket": "^9.0.3", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "sinon": "^9.0.2", "typedoc": "^0.19.2", "typescript": "^4.0.3"}}