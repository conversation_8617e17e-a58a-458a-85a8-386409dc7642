{"version": 3, "file": "GoTrueClient.js", "sourceRoot": "", "sources": ["../../src/GoTrueClient.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,4DAAmC;AACnC,2CAQsB;AACtB,+CAMwB;AACxB,+CAAoD;AAkBpD,IAAA,8BAAkB,GAAE,CAAA,CAAC,8BAA8B;AAEnD,MAAM,eAAe,GAAG;IACtB,GAAG,EAAE,sBAAU;IACf,gBAAgB,EAAE,IAAI;IACtB,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;IACxB,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,2BAAe;CACzB,CAAA;AAED,MAAM,eAAe,GAAG,CAAC,KAAa,EAAU,EAAE;IAChD,IAAI;QACF,mDAAmD;QACnD,yFAAyF;QACzF,qFAAqF;QACrF,8CAA8C;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;KAC7D;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,CAAC,YAAY,cAAc,EAAE;YAC/B,yBAAyB;YACzB,2CAA2C;YAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;SACtD;aAAM;YACL,MAAM,CAAC,CAAA;SACR;KACF;AACH,CAAC,CAAA;AAED,MAAqB,YAAY;IAuB/B;;;;;;;;;;;OAWG;IACH,YAAY,OAUX;QA1BS,wBAAmB,GAA8B,IAAI,GAAG,EAAE,CAAA;QAE1D,mBAAc,GAAW,CAAC,CAAA;QAyBlC,MAAM,QAAQ,mCAAQ,eAAe,GAAK,OAAO,CAAE,CAAA;QACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAC1B,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAA;QACjD,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAA;QAC7C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,IAAI,UAAU,CAAC,YAAY,CAAA;QACpE,IAAI,CAAC,GAAG,GAAG,IAAI,mBAAS,CAAC;YACvB,GAAG,EAAE,QAAQ,CAAC,GAAG;YACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,aAAa,EAAE,QAAQ,CAAC,aAAa;YACrC,KAAK,EAAE,QAAQ,CAAC,KAAK;SACtB,CAAC,CAAA;QACF,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAA;QAE9B,IAAI,QAAQ,CAAC,kBAAkB,IAAI,IAAA,mBAAS,GAAE,IAAI,CAAC,CAAC,IAAA,4BAAkB,EAAC,cAAc,CAAC,EAAE;YACtF,4BAA4B;YAC5B,IAAI,CAAC,iBAAiB,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;gBAChE,IAAI,KAAK,EAAE;oBACT,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;iBACnD;YACH,CAAC,CAAC,CAAA;SACH;IACH,CAAC;IAED;;;;;;;;OAQG;IACG,MAAM,CACV,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAmB,EAC3C,UAII,EAAE;;YAMN,IAAI;gBACF,IAAI,CAAC,cAAc,EAAE,CAAA;gBAErB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GACnB,KAAK,IAAI,QAAQ;oBACf,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAM,EAAE,QAAS,EAAE;wBAChD,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC,CAAC;oBACJ,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAM,EAAE,QAAS,EAAE;wBAChD,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC,CAAC,CAAA;gBAER,IAAI,KAAK,EAAE;oBACT,MAAM,KAAK,CAAA;iBACZ;gBAED,IAAI,CAAC,IAAI,EAAE;oBACT,MAAM,+BAA+B,CAAA;iBACtC;gBAED,IAAI,OAAO,GAAmB,IAAI,CAAA;gBAClC,IAAI,IAAI,GAAgB,IAAI,CAAA;gBAE5B,IAAK,IAAgB,CAAC,YAAY,EAAE;oBAClC,OAAO,GAAG,IAAe,CAAA;oBACzB,IAAI,GAAG,OAAO,CAAC,IAAY,CAAA;oBAC3B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;oBAC1B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;iBACxC;gBAED,IAAK,IAAa,CAAC,EAAE,EAAE;oBACrB,IAAI,GAAG,IAAY,CAAA;iBACpB;gBAED,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC3D;QACH,CAAC;KAAA;IAED;;;;;;;;;;;OAWG;IACG,MAAM,CACV,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAmB,EACzE,UAMI,EAAE;;YAQN,IAAI;gBACF,IAAI,CAAC,cAAc,EAAE,CAAA;gBAErB,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;oBACtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE;wBACzD,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;wBAC1C,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC,CAAC,CAAA;oBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC5C;gBACD,IAAI,KAAK,IAAI,QAAQ,EAAE;oBACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,EAAE;wBAC9C,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC,CAAC,CAAA;iBACH;gBACD,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;oBACtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,EAAE;wBACpD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;wBAC1C,YAAY,EAAE,OAAO,CAAC,YAAY;qBACnC,CAAC,CAAA;oBACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC5C;gBACD,IAAI,KAAK,IAAI,QAAQ,EAAE;oBACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;iBAChD;gBACD,IAAI,YAAY,EAAE;oBAChB,8GAA8G;oBAC9G,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAA;oBAC5D,IAAI,KAAK;wBAAE,MAAM,KAAK,CAAA;oBAEtB,OAAO;wBACL,IAAI,EAAE,IAAI,CAAC,WAAW;wBACtB,OAAO,EAAE,IAAI,CAAC,cAAc;wBAC5B,KAAK,EAAE,IAAI;qBACZ,CAAA;iBACF;gBACD,IAAI,QAAQ,EAAE;oBACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE;wBAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;qBACjC,CAAC,CAAA;iBACH;gBACD,IAAI,IAAI,EAAE;oBACR,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAA;iBAC7C;gBACD,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAA;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC3D;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,SAAS,CACb,MAAuB,EACvB,UAEI,EAAE;;YAMN,IAAI;gBACF,IAAI,CAAC,cAAc,EAAE,CAAA;gBAErB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;gBAEjE,IAAI,KAAK,EAAE;oBACT,MAAM,KAAK,CAAA;iBACZ;gBAED,IAAI,CAAC,IAAI,EAAE;oBACT,MAAM,0CAA0C,CAAA;iBACjD;gBAED,IAAI,OAAO,GAAmB,IAAI,CAAA;gBAClC,IAAI,IAAI,GAAgB,IAAI,CAAA;gBAE5B,IAAK,IAAgB,CAAC,YAAY,EAAE;oBAClC,OAAO,GAAG,IAAe,CAAA;oBACzB,IAAI,GAAG,OAAO,CAAC,IAAY,CAAA;oBAC3B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;oBAC1B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;iBACxC;gBAED,IAAK,IAAa,CAAC,EAAE,EAAE;oBACrB,IAAI,GAAG,IAAY,CAAA;iBACpB;gBAED,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC3D;QACH,CAAC;KAAA;IAED;;;;OAIG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAED;;OAEG;IACG,cAAc;;;YAKlB,IAAI;gBACF,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,cAAc,0CAAE,YAAY,CAAA;oBAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;gBAEzE,gFAAgF;gBAChF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAChD,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAA;gBAEtB,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC1E;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;;KACF;IAED;;OAEG;IACG,MAAM,CACV,UAA0B;;;YAE1B,IAAI;gBACF,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,cAAc,0CAAE,YAAY,CAAA;oBAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;gBAEzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAC/C,IAAI,CAAC,cAAc,CAAC,YAAY,EAChC,UAAU,CACX,CAAA;gBACD,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAA;gBACtB,IAAI,CAAC,IAAI;oBAAE,MAAM,KAAK,CAAC,oBAAoB,CAAC,CAAA;gBAE5C,MAAM,OAAO,mCAAQ,IAAI,CAAC,cAAc,KAAE,IAAI,GAAE,CAAA;gBAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAA;gBAE1C,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACxD;;KACF;IAyBK,UAAU,CACd,IAA8D;;YAE9D,IAAI,OAAgB,CAAA;YAEpB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,qCAAqC;gBACrC,MAAM,aAAa,GAAG,IAAI,CAAA;gBAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;gBACxE,IAAI,KAAK,EAAE;oBACT,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;iBACvC;gBAED,OAAO,GAAG,IAAK,CAAA;aAChB;iBAAM;gBACL,iCAAiC;gBAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;gBAE7C,IAAI,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,IAAI,CAAA;gBAC1C,IAAI,UAAU,GAAG,CAAC,CAAA;gBAClB,IAAI,UAAU,GAAG,CAAC,CAAA;gBAElB,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC1C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;gBAEhF,MAAM,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;gBAE/C,IAAI,MAAM,GAAQ,SAAS,CAAA;gBAC3B,IAAI;oBACF,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;iBAC9B;gBAAC,OAAO,CAAC,EAAE;oBACV,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;iBAC1E;gBAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE;oBAC1E,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;oBACvB,UAAU,GAAG,OAAO,GAAG,MAAM,CAAC,GAAG,CAAA;iBAClC;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;iBACvE;gBAED,IAAI,OAAO,GAAG,UAAU,EAAE;oBACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;oBACxE,IAAI,KAAK,EAAE;wBACT,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;qBACvC;oBAED,OAAO,GAAG,IAAK,CAAA;iBAChB;qBAAM;oBACL,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;oBAC5D,IAAI,KAAK;wBAAE,MAAM,KAAK,CAAA;oBAEtB,OAAO,GAAG;wBACR,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,aAAa;wBACb,UAAU,EAAE,QAAQ;wBACpB,IAAI,EAAE,IAAK;qBACZ,CAAA;iBACF;aACF;YAED,IAAI;gBACF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;gBACvC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAChC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,KAAK,EAAE,CAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;aAC/C;QACH,CAAC;KAAA;IAED;;;OAGG;IACH,OAAO,CAAC,YAAoB;QAC1B,IAAI,CAAC,cAAc,mCACd,IAAI,CAAC,cAAc,KACtB,YAAY,EACZ,UAAU,EAAE,QAAQ,EACpB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAClB,CAAA;QAED,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAA;QAE7C,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACG,iBAAiB,CAAC,OAEvB;;YACC,IAAI;gBACF,IAAI,CAAC,IAAA,mBAAS,GAAE;oBAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAEzD,MAAM,iBAAiB,GAAG,IAAA,4BAAkB,EAAC,mBAAmB,CAAC,CAAA;gBACjE,IAAI,iBAAiB;oBAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAEzD,MAAM,cAAc,GAAG,IAAA,4BAAkB,EAAC,gBAAgB,CAAC,CAAA;gBAC3D,MAAM,sBAAsB,GAAG,IAAA,4BAAkB,EAAC,wBAAwB,CAAC,CAAA;gBAC3E,MAAM,YAAY,GAAG,IAAA,4BAAkB,EAAC,cAAc,CAAC,CAAA;gBACvD,IAAI,CAAC,YAAY;oBAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;gBAC/D,MAAM,UAAU,GAAG,IAAA,4BAAkB,EAAC,YAAY,CAAC,CAAA;gBACnD,IAAI,CAAC,UAAU;oBAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;gBAC3D,MAAM,aAAa,GAAG,IAAA,4BAAkB,EAAC,eAAe,CAAC,CAAA;gBACzD,IAAI,CAAC,aAAa;oBAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;gBACjE,MAAM,UAAU,GAAG,IAAA,4BAAkB,EAAC,YAAY,CAAC,CAAA;gBACnD,IAAI,CAAC,UAAU;oBAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;gBAE3D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;gBAC7C,MAAM,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;gBAEjD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;gBAC5D,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAA;gBAEtB,MAAM,OAAO,GAAY;oBACvB,cAAc;oBACd,sBAAsB;oBACtB,YAAY;oBACZ,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC;oBAChC,UAAU;oBACV,aAAa;oBACb,UAAU;oBACV,IAAI,EAAE,IAAK;iBACZ,CAAA;gBACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,EAAE;oBACzB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;oBAC1B,MAAM,YAAY,GAAG,IAAA,4BAAkB,EAAC,MAAM,CAAC,CAAA;oBAC/C,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;oBACvC,IAAI,YAAY,KAAK,UAAU,EAAE;wBAC/B,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAA;qBAChD;iBACF;gBACD,yBAAyB;gBACzB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAA;gBAEzB,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACtC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,OAAO;;;YACX,MAAM,WAAW,GAAG,MAAA,IAAI,CAAC,cAAc,0CAAE,YAAY,CAAA;YACrD,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;YACxC,IAAI,WAAW,EAAE;gBACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;gBACrD,IAAI,KAAK;oBAAE,OAAO,EAAE,KAAK,EAAE,CAAA;aAC5B;YACD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;;KACvB;IAED;;;OAGG;IACH,iBAAiB,CAAC,QAAmE;QAInF,IAAI;YACF,MAAM,EAAE,GAAW,IAAA,cAAI,GAAE,CAAA;YACzB,MAAM,YAAY,GAAiB;gBACjC,EAAE;gBACF,QAAQ;gBACR,WAAW,EAAE,GAAG,EAAE;oBAChB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBACrC,CAAC;aACF,CAAA;YACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,EAAE,YAAY,CAAC,CAAA;YAC9C,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;SAC3C;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;SAC5C;IACH,CAAC;IAEa,kBAAkB,CAC9B,KAAa,EACb,QAAgB,EAChB,UAGI,EAAE;;;YAEN,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE;oBACtE,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;iBACnC,CAAC,CAAA;gBACF,IAAI,KAAK,IAAI,CAAC,IAAI;oBAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;gBAE3E,IAAI,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,YAAY,MAAI,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,kBAAkB,CAAA,EAAE;oBAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;oBACvB,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;iBACxC;gBAED,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7D;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACvE;;KACF;IAEa,kBAAkB,CAC9B,KAAa,EACb,QAAgB,EAChB,UAEI,EAAE;;;YAEN,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;gBAChF,IAAI,KAAK,IAAI,CAAC,IAAI;oBAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;gBAE3E,IAAI,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,kBAAkB,EAAE;oBAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;oBACvB,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;iBACxC;gBAED,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7D;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aACvE;;KACF;IAEO,qBAAqB,CAC3B,QAAkB,EAClB,UAII,EAAE;QAEN,MAAM,GAAG,GAAW,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACvD,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,CAAC,CAAA;QAEF,IAAI;YACF,6BAA6B;YAC7B,IAAI,IAAA,mBAAS,GAAE,EAAE;gBACf,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAA;aAC3B;YACD,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;SAC7E;QAAC,OAAO,CAAC,EAAE;YACV,gCAAgC;YAChC,IAAI,GAAG;gBAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;YACrF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;SACvE;IACH,CAAC;IAEa,0BAA0B,CAAC,EACvC,QAAQ,EACR,KAAK,EACL,SAAS,EACT,MAAM,EACN,QAAQ,GACiB;;YAKzB,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,QAAQ,CAAC,EAAE;gBAC5D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC;wBAC7D,QAAQ;wBACR,KAAK;wBACL,SAAS;wBACT,MAAM;wBACN,QAAQ;qBACT,CAAC,CAAA;oBACF,IAAI,KAAK,IAAI,CAAC,IAAI;wBAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;oBAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;oBACvB,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;oBACvC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;iBACvD;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;iBAC3D;aACF;YACD,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAA;QAC7F,CAAC;KAAA;IAED;;;OAGG;IACK,eAAe;QACrB,IAAI;YACF,MAAM,IAAI,GAAG,IAAA,8BAAoB,EAAC,IAAI,CAAC,YAAY,EAAE,uBAAW,CAAC,CAAA;YACjE,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAA;YACtB,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,CAAA;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;YAE7C,IAAI,SAAS,IAAI,OAAO,GAAG,yBAAa,KAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,CAAA,EAAE;gBAChE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;gBACjC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;aACxC;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;SAC5B;IACH,CAAC;IAED;;;OAGG;IACW,kBAAkB;;YAC9B,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,sBAAY,EAAC,IAAI,CAAC,YAAY,EAAE,uBAAW,CAAC,CAAA;gBAC/D,IAAI,CAAC,IAAI;oBAAE,OAAO,IAAI,CAAA;gBACtB,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,CAAA;gBAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;gBAE7C,IAAI,SAAS,GAAG,OAAO,GAAG,yBAAa,EAAE;oBACvC,IAAI,IAAI,CAAC,gBAAgB,IAAI,cAAc,CAAC,aAAa,EAAE;wBACzD,IAAI,CAAC,cAAc,EAAE,CAAA;wBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;wBAC5E,IAAI,KAAK,EAAE;4BACT,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;4BAC1B,IACE,KAAK,CAAC,OAAO,KAAK,2BAAe,CAAC,aAAa;gCAC/C,IAAI,CAAC,cAAc,GAAG,2BAAe,CAAC,WAAW,EACjD;gCACA,IAAI,IAAI,CAAC,iBAAiB;oCAAE,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;gCAChE,IAAI,CAAC,iBAAiB,GAAG,UAAU,CACjC,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAC/B,SAAA,2BAAe,CAAC,cAAc,EAAI,IAAI,CAAC,cAAc,CAAA,GAAG,GAAG,CAAC,sBAAsB;iCACnF,CAAA;gCACD,OAAM;6BACP;4BACD,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;yBAC5B;wBACD,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;qBACxB;yBAAM;wBACL,IAAI,CAAC,cAAc,EAAE,CAAA;qBACtB;iBACF;qBAAM,IAAI,CAAC,cAAc,EAAE;oBAC1B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;oBAC/C,IAAI,CAAC,cAAc,EAAE,CAAA;iBACtB;qBAAM;oBACL,sDAAsD;oBACtD,uFAAuF;oBACvF,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;oBACjC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;iBACxC;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAClB,OAAO,IAAI,CAAA;aACZ;QACH,CAAC;KAAA;IAEa,iBAAiB,CAAC,aAAkD;;sCAAlD,EAAA,sBAAgB,IAAI,CAAC,cAAc,0CAAE,aAAa;;YAChF,IAAI;gBACF,IAAI,CAAC,aAAa,EAAE;oBAClB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;iBACvC;gBACD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;gBACxE,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAA;gBACtB,IAAI,CAAC,IAAI;oBAAE,MAAM,KAAK,CAAC,uBAAuB,CAAC,CAAA;gBAE/C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;gBACvB,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAA;gBAC7C,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;gBAEvC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAa,EAAE,CAAA;aAC5C;;KACF;IAEO,qBAAqB,CAAC,KAAsB;QAClD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAA;IACjF,CAAC;IAED;;;OAGG;IACK,YAAY,CAAC,OAAgB;QACnC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAA;QAC7B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAA;QAE/B,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAA;QACpC,IAAI,SAAS,EAAE;YACb,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;YAC7C,MAAM,SAAS,GAAG,SAAS,GAAG,OAAO,CAAA;YACrC,MAAM,4BAA4B,GAAG,SAAS,GAAG,yBAAa,CAAC,CAAC,CAAC,yBAAa,CAAC,CAAC,CAAC,GAAG,CAAA;YACpF,IAAI,CAAC,sBAAsB,CAAC,CAAC,SAAS,GAAG,4BAA4B,CAAC,GAAG,IAAI,CAAC,CAAA;SAC/E;QAED,oDAAoD;QACpD,yBAAyB;QACzB,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,UAAU,EAAE;YAC7C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;SAC1C;IACH,CAAC;IAEO,eAAe,CAAC,cAAuB;QAC7C,MAAM,IAAI,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC,UAAU,EAAE,CAAA;QACrE,IAAA,sBAAY,EAAC,IAAI,CAAC,YAAY,EAAE,uBAAW,EAAE,IAAI,CAAC,CAAA;IACpD,CAAC;IAEa,cAAc;;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,IAAI,IAAI,CAAC,iBAAiB;gBAAE,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YAChE,IAAA,yBAAe,EAAC,IAAI,CAAC,YAAY,EAAE,uBAAW,CAAC,CAAA;QACjD,CAAC;KAAA;IAED;;;OAGG;IACK,sBAAsB,CAAC,KAAa;QAC1C,IAAI,IAAI,CAAC,iBAAiB;YAAE,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAChE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAM;QAEhD,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAS,EAAE;YAC7C,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;YAChD,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;YACnC,IACE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,MAAK,2BAAe,CAAC,aAAa;gBAChD,IAAI,CAAC,cAAc,GAAG,2BAAe,CAAC,WAAW;gBAEjD,IAAI,CAAC,sBAAsB,CAAC,SAAA,2BAAe,CAAC,cAAc,EAAI,IAAI,CAAC,cAAc,CAAA,GAAG,GAAG,CAAC,CAAA,CAAC,sBAAsB;QACnH,CAAC,CAAA,EAAE,KAAK,CAAC,CAAA;QACT,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,KAAK,UAAU;YAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;IACxF,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAA,mBAAS,GAAE,IAAI,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAA,EAAE;YAC/D,OAAO,KAAK,CAAA;SACb;QAED,IAAI;YACF,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAe,EAAE,EAAE;;gBACtD,IAAI,CAAC,CAAC,GAAG,KAAK,uBAAW,EAAE;oBACzB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;oBACjD,IAAI,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,cAAc,0CAAE,YAAY,EAAE;wBAC5C,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;wBAC5C,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;qBACxC;yBAAM;wBACL,IAAI,CAAC,cAAc,EAAE,CAAA;wBACrB,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;qBACzC;iBACF;YACH,CAAC,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;SACjD;IACH,CAAC;IAEO,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAA,mBAAS,GAAE,IAAI,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAA,EAAE;YAC/D,OAAO,KAAK,CAAA;SACb;QAED,IAAI;YACF,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAChD,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;oBAC1C,IAAI,CAAC,kBAAkB,EAAE,CAAA;iBAC1B;YACH,CAAC,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;SAChD;IACH,CAAC;CACF;AA30BD,+BA20BC"}